class RenameMediaGroupsToMediaCollections < ActiveRecord::Migration[8.0]
  def change
    # Rename the table
    rename_table :media_groups, :media_collections

    # Rename foreign key columns
    rename_column :media, :media_group_id, :media_collection_id
    rename_column :blocks, :media_group_id, :media_collection_id

    # Update foreign key constraints
    remove_foreign_key :media, :media_groups if foreign_key_exists?(:media, :media_groups)
    remove_foreign_key :blocks, :media_groups if foreign_key_exists?(:blocks, :media_groups)

    add_foreign_key :media, :media_collections, column: :media_collection_id
    add_foreign_key :blocks, :media_collections, column: :media_collection_id
  end
end
