class MigrateBlockControlsToLocales < ActiveRecord::Migration[8.0]
  def up
    # Dočasně vypni STI pro BlockControl
    BlockControl.inheritance_column = nil

    # Jednodušší přístup pomocí ActiveRecord modelů

    # Najdi všechny websites s více locales
    Website.where.not(available_locales: nil).each do |website|
      locales = website.available_locales || ['cs']
      next if locales.length <= 1

      puts "🌐 Zpracovávám website #{website.id} s locales: #{locales.join(', ')}"

      # Najdi všechny bloky pro tento website
      blocks = Block.joins(:page).where(pages: { website_id: website.id })

      blocks.each do |block|
        controls = block.controls.where(locale: 'cs') # Existující controls mají default 'cs'
        next if controls.empty?

        puts "  📦 Blok #{block.id}: #{controls.count} controls"

        # Pro každý další locale vytvoř kopie
        locales[1..-1].each do |locale|
          controls.each do |original_control|
            new_control = original_control.dup
            new_control.locale = locale
            new_control.save!
          end
          puts "    ✅ Vytvořeno #{controls.count} controls pro #{locale}"
        end
      end
    end

    puts "🎉 Migrace dokončena!"

    # Obnov STI
    BlockControl.inheritance_column = 'type'
  end

  def down
    # Smaž všechny controls kromě těch s locale 'cs'
    BlockControl.where.not(locale: 'cs').delete_all
    puts "⏪ Rollback: zachovány pouze CS controls"
  end
end
