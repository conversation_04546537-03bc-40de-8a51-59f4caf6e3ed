# Vytvoř<PERSON><PERSON> ty<PERSON> mé<PERSON> podle existu<PERSON><PERSON><PERSON><PERSON><PERSON>
gallery_type = MediaType.create(name: "Gallery", description: "Fotogalerie")
feature_type = MediaType.create(name: "Feature", description: "Funkce nebo vlastnost")
article_type = MediaType.create(name: "Article", description: "<PERSON><PERSON><PERSON><PERSON>")
team_type = MediaType.create(name: "Team", description: "<PERSON><PERSON> týmu")
review_type = MediaType.create(name: "Review", description: "Recenze")

# Přidání polí pro Gallery
gallery_type.media_fields.create(field_key: "title", field_type: "string", position: 1, required: true)
gallery_type.media_fields.create(field_key: "image", field_type: "image", position: 2, required: true)

# Přidání polí pro Feature
feature_type.media_fields.create(field_key: "title", field_type: "string", position: 1, required: true)
feature_type.media_fields.create(field_key: "body", field_type: "text", position: 2, required: true)
feature_type.media_fields.create(field_key: "icon", field_type: "icon", position: 3, required: true)

# <PERSON><PERSON>idání polí pro Article
article_type.media_fields.create(field_key: "title", field_type: "string", position: 1, required: true)
article_type.media_fields.create(field_key: "perex", field_type: "text", position: 2, required: true)
article_type.media_fields.create(field_key: "content", field_type: "content", position: 3, required: true)
article_type.media_fields.create(field_key: "image", field_type: "image", position: 4, required: false)
article_type.media_fields.create(field_key: "author", field_type: "string", position: 5, required: false)

# Přidání polí pro Team
team_type.media_fields.create(field_key: "title", field_type: "string", position: 1, required: true)
team_type.media_fields.create(field_key: "body", field_type: "text", position: 2, required: false)
team_type.media_fields.create(field_key: "content", field_type: "content", position: 3, required: true)
team_type.media_fields.create(field_key: "image", field_type: "image", position: 4, required: false)
team_type.media_fields.create(field_key: "job_position", field_type: "string", position: 5, required: false)

# Přidání polí pro Review
review_type.media_fields.create(field_key: "title", field_type: "string", position: 1, required: true)
review_type.media_fields.create(field_key: "body", field_type: "text", position: 2, required: true)
review_type.media_fields.create(field_key: "author", field_type: "string", position: 3, required: false)
review_type.media_fields.create(field_key: "rating", field_type: "rating", position: 4, required: true)

# Migrace existujících médií
Media.find_each do |medium|
  case medium.type
  when "Gallery"
    medium.update(media_type: MediaType.find_by(name: "Gallery"))
  when "Feature"
    medium.update(media_type: MediaType.find_by(name: "Feature"))
  when "Article"
    medium.update(media_type: MediaType.find_by(name: "Article"))
  when "Team"
    medium.update(media_type: MediaType.find_by(name: "Team"))
  when "Review"
    medium.update(media_type: MediaType.find_by(name: "Review"))
  end
end

# Aktualizace media_group s odpovídajícím media_type
MediaGroup.find_each do |group|
  if group.media.any?
    first_medium = group.media.first
    if first_medium.media_type.present?
      group.update(media_type: first_medium.media_type)
    end
  end
end
