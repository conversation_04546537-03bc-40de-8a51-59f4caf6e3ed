# Vytvoření typů médií
feature_type = MediaType.find_or_create_by(name: "Feature") do |mt|
  mt.has_groups = true
end

# Přidání polí pro Feature
MediaField.find_or_create_by(media_type: feature_type, field_key: "title") do |field|
  field.field_type = "string"
  field.position = 1
  field.required = true
end

MediaField.find_or_create_by(media_type: feature_type, field_key: "body") do |field|
  field.field_type = "text"
  field.position = 2
  field.required = true
end

MediaField.find_or_create_by(media_type: feature_type, field_key: "icon") do |field|
  field.field_type = "icon"
  field.position = 3
  field.required = true
end

puts "MediaType 'Feature' created with fields"
