# == Schema Information
#
# Table name: posts
#
#  id           :bigint           not null, primary key
#  author       :string
#  data         :jsonb
#  origin       :string
#  perex        :string
#  published_at :datetime
#  removed_at   :datetime
#  title        :string
#  type         :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  unique_id    :string
#  website_id   :bigint           not null
#
# Indexes
#
#  index_posts_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#

one:
  restaurant: one
  title: MyString
  type: 
  unique_id: MyString
  data: 
  published_at: 2024-07-26 11:14:01

two:
  restaurant: two
  title: MyString
  type: 
  unique_id: MyString
  data: 
  published_at: 2024-07-26 11:14:01
