# == Schema Information
#
# Table name: submission_fields
#
#  id                 :bigint           not null, primary key
#  value              :text
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  form_field_id      :bigint           not null
#  form_submission_id :bigint           not null
#
# Indexes
#
#  index_submission_fields_on_form_field_id       (form_field_id)
#  index_submission_fields_on_form_submission_id  (form_submission_id)
#
# Foreign Keys
#
#  fk_rails_...  (form_field_id => form_fields.id)
#  fk_rails_...  (form_submission_id => form_submissions.id)
#

one:
  form_submission: one
  form_field: one
  value: MyText

two:
  form_submission: two
  form_field: two
  value: MyText
