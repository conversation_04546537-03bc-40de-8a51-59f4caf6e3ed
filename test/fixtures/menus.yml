# == Schema Information
#
# Table name: menus
#
#  id            :bigint           not null, primary key
#  menu_type     :string
#  name          :hstore
#  position      :integer
#  valid_from    :datetime
#  valid_to      :datetime
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  restaurant_id :bigint           not null
#
# Indexes
#
#  index_menus_on_restaurant_id  (restaurant_id)
#
# Foreign Keys
#
#  fk_rails_...  (restaurant_id => restaurants.id)
#

one:
  account: one
  name: MyString
  menu_type: MyString
  valid_from: 2024-05-23 10:07:49
  valid_to: 2024-05-23 10:07:49

two:
  account: two
  name: MyString
  menu_type: MyString
  valid_from: 2024-05-23 10:07:49
  valid_to: 2024-05-23 10:07:49
