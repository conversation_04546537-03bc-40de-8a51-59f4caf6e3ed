# == Schema Information
#
# Table name: restaurants
#
#  id                :bigint           not null, primary key
#  address           :string
#  available_locales :jsonb
#  city              :string
#  country           :string
#  data              :jsonb
#  domain            :string
#  email             :string
#  locale            :string           default("cs")
#  name              :string
#  phone             :string
#  postal_code       :string
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  account_id        :bigint           not null
#
# Indexes
#
#  index_restaurants_on_account_id  (account_id)
#
# Foreign Keys
#
#  fk_rails_...  (account_id => accounts.id)
#

one:
  account: one
  name: MyString
  address: MyString
  city: MyString
  postal_code: MyString
  country: MyString
  phone: MyString
  email: MyString
  domain: MyString

two:
  account: two
  name: MyString
  address: MyString
  city: MyString
  postal_code: MyString
  country: MyString
  phone: MyString
  email: MyString
  domain: MyString
