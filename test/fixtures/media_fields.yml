# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: media_fields
#
#  id            :bigint           not null, primary key
#  field_key     :string
#  field_type    :string
#  position      :integer
#  required      :boolean
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  media_type_id :bigint           not null
#
# Indexes
#
#  index_media_fields_on_media_type_id  (media_type_id)
#
# Foreign Keys
#
#  fk_rails_...  (media_type_id => media_types.id)
#
one:
  media_type: one
  field_key: MyString
  field_type: MyString
  position: 1
  required: false

two:
  media_type: two
  field_key: MyString
  field_type: MyString
  position: 1
  required: false
