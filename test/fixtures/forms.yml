# == Schema Information
#
# Table name: forms
#
#  id         :bigint           not null, primary key
#  enabled    :boolean          default(TRUE)
#  form_type  :integer
#  name       :string
#  options    :jsonb
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  website_id :bigint           not null
#
# Indexes
#
#  index_forms_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#

one:
  name: MyString
  form_type: 1

two:
  name: MyString
  form_type: 1
