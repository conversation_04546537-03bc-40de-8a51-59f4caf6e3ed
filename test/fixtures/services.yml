# == Schema Information
#
# Table name: services
#
#  id           :bigint           not null, primary key
#  options      :jsonb
#  processed_at :datetime
#  type         :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  website_id   :bigint           not null
#
# Indexes
#
#  index_services_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#

one:
  name: MyString
  options: 
  processed_at: 2024-07-16 16:17:35

two:
  name: MyString
  options: 
  processed_at: 2024-07-16 16:17:35
