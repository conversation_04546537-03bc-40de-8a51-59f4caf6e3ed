# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

# == Schema Information
#
# Table name: page_blocks
#
#  id         :bigint           not null, primary key
#  position   :integer          not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  block_id   :bigint           not null
#  page_id    :bigint           not null
#
# Indexes
#
#  index_page_blocks_on_block_id                           (block_id)
#  index_page_blocks_on_block_id_and_page_id_and_position  (block_id,page_id,position) UNIQUE
#  index_page_blocks_on_page_id                            (page_id)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#  fk_rails_...  (page_id => pages.id)
#
one:
  block: one
  page: one
  position: 1

two:
  block: two
  page: two
  position: 1
