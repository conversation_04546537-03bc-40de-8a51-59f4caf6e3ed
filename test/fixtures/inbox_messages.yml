# == Schema Information
#
# Table name: inbox_messages
#
#  id         :bigint           not null, primary key
#  email      :string
#  message    :text
#  name       :string
#  phone      :string
#  status     :integer          default(0)
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  form_id    :bigint           not null
#  website_id :bigint           not null
#
# Indexes
#
#  index_inbox_messages_on_form_id     (form_id)
#  index_inbox_messages_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (form_id => forms.id)
#  fk_rails_...  (website_id => websites.id)
#

one:
  form: one
  message: MyText
  email: MyString
  phone: MyString
  name: MyString
  status: 1

two:
  form: two
  message: MyText
  email: MyString
  phone: MyString
  name: MyString
  status: 1
