<% password_digest = BCrypt::Password.create("password") %>

# == Schema Information
#
# Table name: users
#
#  id              :bigint           not null, primary key
#  email           :string
#  first_name      :string
#  last_name       :string
#  name            :string
#  password_digest :string
#  remember_token  :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#
one:
  email: <EMAIL>
  password_digest: <%= password_digest %>

two:
  email: <EMAIL>
  password_digest: <%= password_digest %>
