# == Schema Information
#
# Table name: reservations
#
#  id         :bigint           not null, primary key
#  date       :date
#  email      :string
#  guests     :integer
#  name       :string
#  note       :text
#  phone      :string
#  status     :integer          default("pending")
#  time       :datetime
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  website_id :bigint           not null
#
# Indexes
#
#  index_reservations_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#

one:
  restaurant: one
  name: MyString
  email: MyString
  phone: MyString
  date: 2024-07-29 16:47:46
  time: 2024-07-29 16:47:46
  people: 1
  note: MyText
  status: 1

two:
  restaurant: two
  name: MyString
  email: MyString
  phone: MyString
  date: 2024-07-29 16:47:46
  time: 2024-07-29 16:47:46
  people: 1
  note: MyText
  status: 1
