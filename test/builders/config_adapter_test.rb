require 'test_helper'

class ConfigAdapterTest < ActiveSupport::TestCase
  test "returns correct adapter for Block" do
    block = Block.new
    adapter = ConfigAdapter.for(block)
    
    assert_instance_of BlockConfigAdapter, adapter
  end
  
  test "returns correct adapter for String" do
    adapter = ConfigAdapter.for("hero001")
    
    assert_instance_of YamlConfigAdapter, adapter
  end
  
  test "raises error for unsupported type" do
    assert_raises ArgumentError do
      ConfigAdapter.for(123)
    end
  end
end
