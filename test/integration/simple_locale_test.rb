require "test_helper"

class SimpleLocaleTest < Minitest::Test
  def test_block_control_locale_functionality
    # Test základní funkcionalita bez složitého setup/teardown
    
    # Vyt<PERSON>ř základní data
    account = Account.create!(name: "Test")
    website = Website.create!(
      account: account,
      name: "Test",
      email: "<EMAIL>",
      phone: "123",
      locale: "cs",
      available_locales: ["cs", "en"]
    )
    
    ActsAsTenant.current_tenant = website
    
    page = Page.create!(
      website: website,
      title: "Test",
      locale: "cs",
      type: "Content"
    )
    
    block = Block.create!(
      page: page,
      name: "test",
      options: {}
    )
    
    # <PERSON>yt<PERSON><PERSON> controls pro oba jazyky
    cs_control = BlockControl.create!(
      block: block,
      type: "BlockControl",
      text: "Český text",
      position: 1,
      locale: "cs",
      options: {}
    )
    
    en_control = BlockControl.create!(
      block: block,
      type: "BlockControl", 
      text: "English text",
      position: 1,
      locale: "en",
      options: {}
    )
    
    # Test scope for_locale
    cs_controls = block.controls.for_locale("cs")
    en_controls = block.controls.for_locale("en")
    
    assert_equal 1, cs_controls.count
    assert_equal 1, en_controls.count
    assert_equal "<PERSON>eský text", cs_controls.first.text
    assert_equal "English text", en_controls.first.text
    
    puts "✅ Scope for_locale funguje"
    
    # Test helper metoda
    cs_helper = block.controls_for_locale("cs")
    en_helper = block.controls_for_locale("en")
    
    assert_equal cs_control, cs_helper.first
    assert_equal en_control, en_helper.first
    
    puts "✅ Helper metoda funguje"
    
    # Test nezávislost
    cs_control.update!(text: "Nový český text")
    en_control.reload
    
    assert_equal "Nový český text", cs_control.text
    assert_equal "English text", en_control.text
    
    puts "✅ Locales jsou nezávislé"
    
    # Test validace locale - locale má default hodnotu, takže testujeme jinak
    invalid_control = BlockControl.new(
      block: block,
      type: "BlockControl",
      text: "Test",
      position: 2,
      locale: nil  # Explicitně nastavíme nil
    )

    refute invalid_control.valid?
    assert_includes invalid_control.errors.attribute_names, :locale

    puts "✅ Validace locale funguje"
    
    # Test migrace dat
    total = block.controls.count
    cs_count = block.controls.for_locale("cs").count  
    en_count = block.controls.for_locale("en").count
    
    assert_equal 2, total
    assert_equal 1, cs_count
    assert_equal 1, en_count
    
    puts "✅ Migrace vytvořila správný počet controls"
    puts "   Celkem: #{total}, CS: #{cs_count}, EN: #{en_count}"
    
    puts "\n🎉 Všechny testy prošly úspěšně!"
    
  rescue => e
    puts "❌ Chyba v testu: #{e.message}"
    raise e
  ensure
    # Cleanup bez problémů s asociacemi
    begin
      ActsAsTenant.without_tenant do
        BlockControl.delete_all
        Block.delete_all  
        Page.delete_all
        Website.delete_all
        Account.delete_all
      end
    rescue => e
      puts "⚠️ Cleanup chyba: #{e.message}"
    end
  end
end
