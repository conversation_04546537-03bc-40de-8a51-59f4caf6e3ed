require "test_helper"

class BlockControlsLocaleTest < Minitest::Test
  def setup
    # Vytvoř testovací data
    @account = Account.create!(name: "Test Account")
    @website = Website.create!(
      account: @account,
      name: "Test Website",
      email: "<EMAIL>",
      phone: "+************",
      locale: "cs",
      available_locales: ["cs", "en"]
    )
    
    # Nastav tenant
    ActsAsTenant.current_tenant = @website
    
    @cs_page = Page.create!(
      website: @website,
      title: "Česká stránka",
      locale: "cs",
      type: "Content"
    )
    
    @en_page = Page.create!(
      website: @website,
      title: "English page",
      locale: "en",
      type: "Content"
    )
    
    @block = Block.create!(
      page: @cs_page,
      name: "test_block",
      options: {}
    )
    
    # Vytvoř controls pro oba jazyky (simulace migrace)
    @cs_control = BlockControl.create!(
      block: @block,
      type: "BlockControl",
      text: "Český nadpis",
      position: 1,
      locale: "cs",
      options: {}
    )

    @en_control = BlockControl.create!(
      block: @block,
      type: "BlockControl",
      text: "English heading",
      position: 1,
      locale: "en",
      options: {}
    )
  end

  def teardown
    # Explicitní mazání bez ActsAsTenant
    @cs_control&.destroy
    @en_control&.destroy
    @block&.destroy
    @cs_page&.destroy
    @en_page&.destroy
    @website&.destroy
    @account&.destroy
  end

  def test_block_controls_for_locale_scope
    # Test scope for_locale
    cs_controls = @block.controls.for_locale("cs")
    en_controls = @block.controls.for_locale("en")
    
    assert_equal 1, cs_controls.count
    assert_equal 1, en_controls.count
    
    assert_equal "Český nadpis", cs_controls.first.text
    assert_equal "English heading", en_controls.first.text
    
    puts "✅ Scope for_locale funguje správně"
  end

  def test_block_controls_for_locale_helper
    # Test helper metoda v Block modelu
    cs_controls = @block.controls_for_locale("cs")
    en_controls = @block.controls_for_locale("en")
    
    assert_equal 1, cs_controls.count
    assert_equal 1, en_controls.count
    
    assert_equal @cs_control, cs_controls.first
    assert_equal @en_control, en_controls.first
    
    puts "✅ Helper metoda controls_for_locale funguje"
  end

  def test_locale_validation
    # Test validace locale
    control = BlockControl.new(
      block: @block,
      type: "BlockControl",
      text: "Test",
      position: 2
      # locale chybí
    )
    
    refute control.valid?
    assert_includes control.errors[:locale], "can't be blank"
    
    puts "✅ Validace locale funguje"
  end

  def test_different_locales_independent
    # Test, že změna v jednom locale neovlivní druhý
    
    # Změň český text
    @cs_control.update!(text: "Nový český nadpis")
    
    # Zkontroluj, že anglický text zůstal nezměněn
    @en_control.reload
    assert_equal "English heading", @en_control.text
    assert_equal "Nový český nadpis", @cs_control.text
    
    puts "✅ Locales jsou nezávislé"
  end

  def test_same_position_different_locales
    # Test, že stejná pozice může existovat pro různé locales
    assert_equal 1, @cs_control.position
    assert_equal 1, @en_control.position
    
    # Oba controls mají pozici 1, ale v různých locales
    cs_controls = @block.controls.for_locale("cs").where(position: 1)
    en_controls = @block.controls.for_locale("en").where(position: 1)
    
    assert_equal 1, cs_controls.count
    assert_equal 1, en_controls.count
    
    puts "✅ Pozice fungují nezávisle per locale"
  end

  def test_migration_created_both_locales
    # Test, že migrace vytvořila controls pro oba jazyky
    total_controls = @block.controls.count
    cs_controls = @block.controls.for_locale("cs").count
    en_controls = @block.controls.for_locale("en").count
    
    assert_equal 2, total_controls
    assert_equal 1, cs_controls
    assert_equal 1, en_controls
    
    puts "✅ Migrace vytvořila controls pro oba jazyky"
    puts "   Celkem: #{total_controls}, CS: #{cs_controls}, EN: #{en_controls}"
  end

  def test_block_shared_visual_settings
    # Test, že vizuální nastavení bloku je sdílené
    original_options = @block.options
    
    # Změň options bloku
    @block.update!(options: { "theme" => "dark" })
    
    # Zkontroluj, že se změna projeví pro oba jazyky
    cs_controls = @block.controls_for_locale("cs")
    en_controls = @block.controls_for_locale("en")
    
    # Oba controls patří ke stejnému bloku se stejnými options
    assert_equal @block.options, { "theme" => "dark" }
    assert_equal @block, cs_controls.first.block
    assert_equal @block, en_controls.first.block
    
    puts "✅ Vizuální nastavení bloku je sdílené"
  end
end
