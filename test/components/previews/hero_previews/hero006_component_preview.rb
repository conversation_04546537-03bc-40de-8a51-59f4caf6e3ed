class HeroPreviews::Hero006ComponentPreview < Lookbook::Preview
  # @param theme select { choices: [default, autumn_harvest, tropical_forest, sunset_glow, winter_night, deep_sea] }
  # @!group Sizes
  def primary_light(theme: "default")
    render BlockComponent.new(Blocks::HeroTypes::Hero006.init(accent: "primary-light"))
  end

  def primary_dark(theme: "default")
    render BlockComponent.new(Blocks::HeroTypes::Hero006.init(accent: "primary-dark"))
  end

  def secondary_light(theme: "default")
    render BlockComponent.new(Blocks::HeroTypes::Hero006.init(accent: "secondary-light"))
  end

  def secondary_dark(theme: "default")
    render BlockComponent.new(Blocks::HeroTypes::Hero006.init(accent: "secondary-dark"))
  end

  def white(theme: "default")
    render BlockComponent.new(Blocks::HeroTypes::Hero006.init(accent: "white"))
  end
end