# == Schema Information
#
# Table name: media_blocks
#
#  id           :bigint           not null, primary key
#  author       :string
#  body         :text
#  data         :jsonb
#  origin       :string
#  perex        :string
#  published_at :datetime
#  removed_at   :datetime
#  title        :string
#  type         :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  unique_id    :string
#  website_id   :bigint           not null
#
# Indexes
#
#  index_contents_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#
require "test_helper"

class Contents::FeatureTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
