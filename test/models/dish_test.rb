# == Schema Information
#
# Table name: dishes
#
#  id              :bigint           not null, primary key
#  description     :hstore
#  name            :hstore
#  price           :decimal(10, 2)
#  price_eur       :decimal(10, 2)
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  menu_section_id :bigint           not null
#
# Indexes
#
#  index_dishes_on_menu_section_id  (menu_section_id)
#
# Foreign Keys
#
#  fk_rails_...  (menu_section_id => menu_sections.id)
#
require "test_helper"

class DishTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
