# == Schema Information
#
# Table name: media_collections
#
#  id              :bigint           not null, primary key
#  collection_type :string
#  name            :string
#  source          :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  website_id      :bigint           not null
#
# Indexes
#
#  index_media_collections_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#
require "test_helper"

class MediaCollectionTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
