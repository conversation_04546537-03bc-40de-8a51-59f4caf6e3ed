# == Schema Information
#
# Table name: menu_sections
#
#  id         :bigint           not null, primary key
#  name       :string
#  position   :integer
#  valid_date :date
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  menu_id    :bigint           not null
#
# Indexes
#
#  index_menu_sections_on_menu_id  (menu_id)
#
# Foreign Keys
#
#  fk_rails_...  (menu_id => pricings.id)
#
require "test_helper"

class MenuSectionTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
