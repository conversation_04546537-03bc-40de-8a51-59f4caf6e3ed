require "test_helper"

class Webhooks::InstagramProcessorTest < ActiveSupport::TestCase
  def setup
    @account = Account.create!(name: "Test Account")
    @website = Website.create!(
      account: @account,
      name: "Test Website",
      domain: "test.com",
      email: "<EMAIL>",
      phone: "+************",
      social_networks: {
        instagram: "https://www.instagram.com/test_account"
      }
    )

    @webhook_payload = {
      "resource" => {
        "defaultDatasetId" => "test_dataset_id"
      }
    }
    @webhook = Webhook.create!(
      event_type: "instagram",
      payload: @webhook_payload
    )

    @instagram_item = {
      "id" => "instagram_post_123",
      "caption" => "Test Instagram post caption",
      "type" => "IMAGE",
      "displayUrl" => "https://example.com/image.jpg",
      "url" => "https://instagram.com/p/test123",
      "timestamp" => "2023-01-01T12:00:00Z",
      "inputUrl" => "https://www.instagram.com/test_account"
    }

    # Create Gallery media type for tests
    @gallery_media_type = MediaType.create!(name: "Gallery")
  end

  test "should create Instagram MediaGroup and Media when processing webhook" do
    # Mock the Apify client calls
    mock_client = Minitest::Mock.new
    mock_client.expect :get_dataset_items, [@instagram_item], ["test_dataset_id"]

    Apify::Client.stub :new, mock_client do
      # Mock URI.open to avoid actual HTTP requests
      mock_io = StringIO.new("fake image data")
      URI.stub :open, mock_io do
        assert_difference "MediaGroup.count", 1 do
          assert_difference "Media.count", 1 do
            Webhooks::InstagramProcessor.process(@webhook)
          end
        end
      end
    end

    # Verify MediaGroup was created correctly
    instagram_group = MediaGroup.instagram.first
    assert_not_nil instagram_group
    assert_equal "Instagram", instagram_group.source
    assert_equal @website, instagram_group.website
    assert_equal "Instagram Posts", instagram_group.name

    # Verify Media was created correctly
    media_item = instagram_group.media.first
    assert_not_nil media_item
    assert_equal "instagram_post_123", media_item.unique_id
    assert_equal "Test Instagram post caption", media_item.caption
    assert_equal "IMAGE", media_item.media_type
    assert_equal "https://example.com/image.jpg", media_item.media_url
    assert_equal "https://instagram.com/p/test123", media_item.url
    assert_equal "Instagram", media_item.origin

    mock_client.verify
  end

  test "should not create duplicate Media for same Instagram post" do
    # Create existing MediaGroup and Media
    instagram_group = InstagramMediaGroup.find_or_create_for_website(@website)
    existing_media = instagram_group.media.create!(
      unique_id: "instagram_post_123",
      title: "Existing post",
      origin: "Instagram"
    )

    # Mock the Apify client calls
    mock_client = Minitest::Mock.new
    mock_client.expect :get_dataset_items, [@instagram_item], ["test_dataset_id"]
    mock_client.expect :get_key_value_store_items, {
      "userData" => {
        "data" => [
          {
            "url" => "https://www.instagram.com/test_account/",
            "website_id" => @website.id
          }
        ]
      }
    }, ["test_store_id"]

    Apify::Client.stub :new, mock_client do
      # Mock URI.open to avoid actual HTTP requests
      mock_io = StringIO.new("fake image data")
      URI.stub :open, mock_io do
        assert_no_difference "Media.count" do
          Webhooks::InstagramProcessor.process(@webhook)
        end
      end
    end

    # Verify the existing media was updated
    existing_media.reload
    assert_equal "Test Instagram post caption", existing_media.caption
    assert_equal "IMAGE", existing_media.media_type

    mock_client.verify
  end

  test "should skip items for non-existent websites" do
    non_existent_item = @instagram_item.merge(
      "inputUrl" => "https://www.instagram.com/non_existent_account/"
    )

    # Mock the Apify client calls
    mock_client = Minitest::Mock.new
    mock_client.expect :get_dataset_items, [non_existent_item], ["test_dataset_id"]
    mock_client.expect :get_key_value_store_items, {
      "userData" => {
        "data" => [
          {
            "url" => "https://www.instagram.com/test_account/",
            "website_id" => @website.id
          }
        ]
      }
    }, ["test_store_id"]

    Apify::Client.stub :new, mock_client do
      assert_no_difference "MediaGroup.count" do
        assert_no_difference "Media.count" do
          Webhooks::InstagramProcessor.process(@webhook)
        end
      end
    end

    mock_client.verify
  end
end
