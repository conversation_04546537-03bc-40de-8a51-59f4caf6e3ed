# == Schema Information
#
# Table name: reservations
#
#  id         :bigint           not null, primary key
#  date       :date
#  email      :string
#  guests     :integer
#  name       :string
#  note       :text
#  phone      :string
#  status     :integer          default("pending")
#  time       :datetime
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  website_id :bigint           not null
#
# Indexes
#
#  index_reservations_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#
require "test_helper"

class ReservationTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
