# == Schema Information
#
# Table name: block_media
#
#  id         :bigint           not null, primary key
#  position   :integer          not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  block_id   :bigint           not null
#  media_id   :bigint           not null
#
# Indexes
#
#  index_block_media_on_block_id  (block_id)
#  index_block_media_on_media_id  (media_id)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#  fk_rails_...  (media_id => media.id)
#
require "test_helper"

class BlockMediumTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
