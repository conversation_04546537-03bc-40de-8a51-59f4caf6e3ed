# == Schema Information
#
# Table name: opening_hours
#
#  id           :bigint           not null, primary key
#  closing_hour :time
#  date         :datetime
#  day          :integer          default(0)
#  opening_hour :time
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  website_id   :bigint           not null
#
# Indexes
#
#  index_opening_hours_on_website_id           (website_id)
#  index_opening_hours_on_website_id_and_date  (website_id,date) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#
require "test_helper"

class OpeningHourTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
