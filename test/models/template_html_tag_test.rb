# == Schema Information
#
# Table name: template_html_tags
#
#  id          :bigint           not null, primary key
#  active      :boolean
#  content     :text
#  name        :string
#  position    :integer
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  template_id :bigint           not null
#
# Indexes
#
#  index_template_html_tags_on_template_id  (template_id)
#
# Foreign Keys
#
#  fk_rails_...  (template_id => templates.id)
#

require "test_helper"

class TemplateHtmlTagTest < ActiveSupport::TestCase
  # test "the truth" do
  #   assert true
  # end
end
