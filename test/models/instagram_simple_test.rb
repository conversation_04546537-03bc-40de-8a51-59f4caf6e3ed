require "test_helper"

class InstagramSimpleTest < ActiveSupport::TestCase
  self.use_transactional_tests = false

  def setup_fixtures
    # Skip fixtures loading
  end
  test "should create Instagram MediaGroup and Media" do
    # Create test data
    account = Account.create!(name: "Test Account")
    website = Website.create!(
      account: account,
      name: "Test Website",
      domain: "test.com",
      email: "<EMAIL>",
      phone: "+************"
    )

    # Create Instagram MediaGroup
    instagram_group = InstagramMediaGroup.find_or_create_for_website(website)

    assert_not_nil instagram_group
    assert_equal "Instagram", instagram_group.source
    assert_equal "Instagram Posts", instagram_group.name
    assert_equal "InstagramMediaGroup", instagram_group.type
    assert_equal website, instagram_group.website

    # Test adding Instagram post
    post_data = {
      id: "instagram_post_123",
      caption: "Test Instagram post",
      type: "IMAGE",
      displayUrl: "https://example.com/image.jpg",
      url: "https://instagram.com/p/test123",
      timestamp: "2023-01-01T12:00:00Z"
    }

    media_item = instagram_group.add_instagram_post(post_data)

    assert_not_nil media_item
    puts "Media item errors: #{media_item.errors.full_messages}" unless media_item.persisted?
    assert media_item.persisted?, "Media item should be saved to database. Errors: #{media_item.errors.full_messages}"
    assert_equal "instagram_post_123", media_item.unique_id
    assert_equal "Test Instagram post", media_item.caption
    assert_equal "Instagram", media_item.origin

    # Check data structure
    assert_not_nil media_item.data
    assert_equal "IMAGE", media_item.data["media_type"]
    assert_equal "https://example.com/image.jpg", media_item.data["media_url"]
    assert_equal "https://instagram.com/p/test123", media_item.data["url"]
  end
end
