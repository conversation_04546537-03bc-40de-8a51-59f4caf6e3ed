const defaultTheme = require("tailwindcss/defaultTheme");

module.exports = {
  content: [
    './app/views/**/*.rb',
    './app/views/**/*.html.erb',
    './app/helpers/**/*.rb',
    './app/assets/stylesheets/**/*.css',
    './app/javascript/**/*.js',
    './app/views/**/*.turbo_stream.erb',
    './lib/tailwind_parser.rb',
  ],
  theme: {
    container: {
      center: true,
      padding: {
        DEFAULT: '1.5rem',
        '2xl': '10rem',
      }
    },
    extend: {
      fontFamily: {
        sans: ["Inter var", ...defaultTheme.fontFamily.sans],
      },
      colors: {
        avocado: {
          '50': '#f5f4f0',
          '100': '#e9e9de',
          '200': '#d5d5c1',
          '300': '#babb9b',
          '400': '#95976b',
          '500': '#82855d',
          '600': '#666947',
          '700': '#4f5239',
          '800': '#414331',
          '900': '#393b2c',
          '950': '#1d1e15',
        },
        brown: {
          50: '#fdf8f6',
          100: '#f2e8e5',
          200: '#eaddd7',
          300: '#e0cec7',
          400: '#d2bab0',
          500: '#bfa094',
          600: '#a18072',
          700: '#977669',
          800: '#846358',
          900: '#43302b',
        },
        'primary': {
          DEFAULT: 'rgb(var(--primary-color) / <alpha-value>)',
          '500': 'rgb(var(--primary-color) / <alpha-value>)',
        },
        'secondary': {
          DEFAULT: 'rgb(var(--secondary-color) / <alpha-value>)',
          '500': 'rgb(var(--secondary-color) / <alpha-value>)',
        },
        'accent': {
          DEFAULT: 'rgb(var(--accent-color) / <alpha-value>)',
          '500': 'rgb(var(--accent-color) / <alpha-value>)',
        },
        'dark': {
          DEFAULT: 'rgb(var(--neutral-dark-color) / <alpha-value>)',
          '500': 'rgb(var(--neutral-dark-color) / <alpha-value>)',
        },
        'accent-text': { DEFAULT: 'rgb(var(--accent-text-color) / <alpha-value>)' }
      },
    },
  },
  safelist: [
    {
      pattern: /text-(sm|md|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|8xl|9xl)/,
      variants: ['sm'],
    },
    {
      pattern: /my-(0|1|2|3|4|5|6|8|10|12|16)/,
      variants: ['sm'],
    },
    {
      pattern: /opacity-(0|5|10|20|25|30|40|50|60|70|75|80|90|95)/,
      variants: ['hover'],
    },
    {
      pattern: /font-(bold|light|normal|semibold|medium)/,
    },
    {
      pattern: /grid-cols-(1|2|3|4|5|6|7|8|9|10|11|12)/,
      variants: ['sm', 'md', 'lg'],
    },
    {
      pattern: /gap-(1|2|3|4|5|6)/,
      variants: ['sm'],
    },
    {
      pattern: /border-(2|4|8)/,
    },
      'w-1/2',
    'bg-primary',
    'bg-primary-500',
    'max-w-3xl',
    'max-w-4xl',
    'max-w-5xl',
    'max-w-6xl',
    'max-w-7xl',
    'max-w-8xl',
    'max-w-9xl',
      'mx-auto',
    'sticky',
    'text-center',
    'order-1',
    'order-2',
    'order-3',
    'order-4',
  ],
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
