desc "Migrate existing Instagram::Post records to new Media/MediaGroup structure"
task migrate_instagram_posts: :environment do
  puts "Starting migration of Instagram posts..."

  # Find all Instagram posts
  instagram_posts = Instagram::Post.all
  puts "Found #{instagram_posts.count} Instagram posts to migrate"

  # Group posts by website (we'll need to determine this somehow)
  # For now, we'll create a default website or use the first one
  default_website = Website.first

  unless default_website
    puts "No website found. Please create a website first."
    exit
  end

  # Create Instagram MediaCollection for the website
  instagram_collection = InstagramMediaCollection.find_or_create_for_website(default_website)
  puts "Created/found Instagram MediaCollection: #{instagram_collection.name}"

  migrated_count = 0
  skipped_count = 0

  instagram_posts.each do |post|
    begin
      # Check if already migrated
      existing_media = instagram_collection.media.find_by(unique_id: post.post_id)

      if existing_media
        puts "Skipping post #{post.post_id} - already migrated"
        skipped_count += 1
        next
      end

      # Create new Media record
      media_item = instagram_collection.media.create!(
        unique_id: post.post_id,
        title: post.caption&.truncate(100) || "Instagram Post",
        caption: post.caption,
        media_type: post.media_type,
        media_url: post.media_url,
        url: post.url,
        published_at: post.published_at,
        origin: "Instagram"
      )

      # Copy attached files
      if post.preview.attached?
        media_item.image.attach(post.preview.blob)
      end

      puts "Migrated post #{post.post_id} -> Media ##{media_item.id}"
      migrated_count += 1

    rescue => e
      puts "Error migrating post #{post.post_id}: #{e.message}"
    end
  end

  puts "Migration completed!"
  puts "Migrated: #{migrated_count} posts"
  puts "Skipped: #{skipped_count} posts"
  puts "Total: #{instagram_posts.count} posts"
end

desc "Migrate Instagram posts for specific website"
task :migrate_instagram_posts_for_website, [:website_id] => :environment do |t, args|
  website_id = args[:website_id]

  unless website_id
    puts "Usage: rake migrate_instagram_posts_for_website[website_id]"
    exit
  end

  website = Website.find(website_id)
  puts "Migrating Instagram posts for website: #{website.name}"

  # Create Instagram MediaCollection for the website
  instagram_collection = InstagramMediaCollection.find_or_create_for_website(website)
  puts "Created/found Instagram MediaCollection: #{instagram_collection.name}"

  # Find all Instagram posts (you might want to filter by some criteria)
  instagram_posts = Instagram::Post.all
  puts "Found #{instagram_posts.count} Instagram posts to migrate"

  migrated_count = 0
  skipped_count = 0

  instagram_posts.each do |post|
    begin
      # Check if already migrated
      existing_media = instagram_collection.media.find_by(unique_id: post.post_id)

      if existing_media
        puts "Skipping post #{post.post_id} - already migrated"
        skipped_count += 1
        next
      end

      # Create new Media record
      media_item = instagram_collection.media.create!(
        unique_id: post.post_id,
        title: post.caption&.truncate(100) || "Instagram Post",
        caption: post.caption,
        media_type: post.media_type,
        media_url: post.media_url,
        url: post.url,
        published_at: post.published_at,
        origin: "Instagram"
      )

      # Copy attached files
      if post.preview.attached?
        media_item.image.attach(post.preview.blob)
      end

      puts "Migrated post #{post.post_id} -> Media ##{media_item.id}"
      migrated_count += 1

    rescue => e
      puts "Error migrating post #{post.post_id}: #{e.message}"
    end
  end

  puts "Migration completed for website #{website.name}!"
  puts "Migrated: #{migrated_count} posts"
  puts "Skipped: #{skipped_count} posts"
  puts "Total: #{instagram_posts.count} posts"
end
