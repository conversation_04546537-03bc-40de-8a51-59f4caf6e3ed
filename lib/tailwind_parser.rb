class TailwindParser
  TAILWIND_MAPPING = {
    /^my-(\d+)$/ => ->(m) { { margin: "my-#{m[1]}" } },
    /^py-(\d+)$/ => ->(m) { { padding: "py-#{m[1]}" } },
    /^space-y-(\d+)$/ => ->(m) { { gap: "space-y-#{m[1]}" } },
    /^text-(sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl|7xl|8xl)$/ => ->(m) { { font_size: "text-#{m[1]}" } },
    /^font-(light|normal|medium|semibold|bold|extrabold|black)$/ => ->(m) { { font_weight: "font-#{m[1]}" } },
    /^max-w-(\d+)(\w+)$/ => ->(m) { { max_width: "max-w-#{m[1]}#{m[2]}" } },
    /^flex$/ => ->(_) { { display: "flex" } },
    /^grid$/ => ->(_) { { display: "grid" } },
    /^block$/ => ->(_) { { display: "block" } }
  }.freeze

  def self.parse(classes)
    classes.split.each_with_object({}) do |klass, result|
      TAILWIND_MAPPING.each do |regex, handler|
        match = klass.match(regex)
        next unless match

        new_entry = handler.call(match)
        new_entry.each do |key, value|
          if result.key?(key) && result[key].is_a?(Array)
            result[key] << value
          elsif result.key?(key)
            result[key] = [ result[key], value ].flatten
          else
            result[key] = value
          end
        end
      end
    end
  end

  # ['text-sm', 'my-1'] => { font_size: 1, margin: 2 }
  def self.from_classes_to_values(classes)
    return
    result = {}

    # Split the string into an array of classes
    classes_array = classes.split(" ")

    font_size_class = classes_array.find { |c| c.start_with?("text-") }
    if font_size_class
      result[:font_size] = font_size_class
    end

    # Reverse map the font_weight from Tailwind CSS class to value
    font_weight_class = classes_array.find { |c| c.start_with?("font-") }
    if font_weight_class
      font_weight_value = font_weight_class.sub("font-", "")
      result[:font_weight] = font_weight_value
    end

    # Reverse map the margin from Tailwind CSS class to value
    margin_class = classes_array.find { |c| margins.value?(c) }
    if margin_class
      margin_value = margins.key(margin_class)
      result[:margin] = margin_value
    end

    padding_class = classes_array.find { |c| paddings.value?(c) }
    if padding_class
      padding_value = paddings.key(padding_class)
      result[:padding] = padding_value
    end

    gap_class = classes_array.find { |c| gaps.value?(c) }
    if gap_class
      gap_value = gaps.key(gap_class)
      result[:gap] = gap_value
    end

    alignment_class = classes_array.find { |c| alignments.value?(c) }
    if alignment_class
      alignment_value = alignments.key(alignment_class)
      result[:alignment] = alignment_value
    end

    container_class = classes_array.find { |c| containers.value?(c) }
    if container_class
      container_value = containers.key(container_class)
      result[:container] = container_value
    end

    result
  end

  # { font_size: 1, margin: 2 } => ['text-sm', 'my-1']
  def self.from_values_to_classes(hash)
    result = []

    # Map the font_size to a Tailwind CSS class
    font_size_class = hash["font_size"]
    result << font_size_class if font_size_class

    # Map the font_weight to a Tailwind CSS class
    font_weight_class = "font-#{hash[:font_weight]}" if hash[:font_weight]
    result << font_weight_class if font_weight_class

    # Map the margin to a Tailwind CSS class
    margin_class = margins[hash["margin"].to_i] if hash["margin"]
    result << margin_class if margin_class

    padding_class = paddings[hash["padding"].to_i] if hash["padding"]
    result << padding_class if padding_class

    gap_class = gaps[hash["gap"].to_i] if hash["gap"]
    result << gap_class if gap_class

    alignment_class = alignments[hash["alignment"]] if hash["alignment"]
    result << alignment_class if alignment_class

    container_class = containers[hash["container"].to_i] if hash["container"]
    result << container_class if container_class

    result.join(" ")
  end

  def self.font_sizes
    %w[text-sm text-base text-lg text-xl text-2xl text-3xl text-4xl text-5xl text-6xl text-7xl text-8xl]
  end

  def self.containers
    {
      1 => "media_blocks",
      2 => "max-w-5xl",
      3 => "max-w-full"
    }
  end

  def self.margins
    {
      0 => "m-0",
      1 => "my-0.5",
      2 => "my-1",
      3 => "my-2",
      4 => "my-4",
      5 => "my-6",
      6 => "my-8",
      7 => "my-10",
      8 => "my-12",
      9 => "my-16",
      10 => "my-20",
      11 => "my-24",
      12 => "my-32"
    }
  end

  def self.alignments
    {
      "left" => "text-left",
      "center" => "text-center",
      "right" => "text-right"
    }
  end

  def self.paddings
    {
      0 => "p-0",
      1 => "py-0.5",
      2 => "py-1",
      3 => "py-2",
      4 => "py-4",
      5 => "py-6",
      6 => "py-8",
      7 => "py-10",
      8 => "py-12",
      9 => "py-16",
      10 => "py-20",
      11 => "py-24",
      12 => "py-32"
    }
  end

  def self.gaps
    {
      0 => "gap-y-0",
      1 => "gap-y-1",
      2 => "gap-y-2",
      3 => "gap-y-4",
      4 => "gap-y-6",
      5 => "gap-y-8",
      6 => "gap-y-10",
      7 => "gap-y-12",
      8 => "gap-y-16",
      9 => "gap-y-20",
      10 => "gap-y-24"
    }
  end
end
