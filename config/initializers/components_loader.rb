# Načtení YAML konfigurace do Rails config.x
components_config = YAML.load_file(Rails.root.join('config', 'components/components.yml')).deep_symbolize_keys

# Uložení konfigurace do Rails.application.config.x.components
Rails.application.config.x.components = components_config.flat_map do |category, components|
  components.map do |name, klass|
    [name.to_sym, klass]
  end
end.to_h

Rails.application.config.x.components_by_type = components_config
