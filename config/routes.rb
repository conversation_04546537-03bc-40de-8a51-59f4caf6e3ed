Rails.application.routes.draw do
  resource :session
  resources :passwords, param: :token
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  direct :cdn_image do |model, options|
    expires_in = options.delete(:expires_in) { ActiveStorage.urls_expire_in }

    if model.respond_to?(:signed_id)
      if Rails.env.production?
        route_for(:rails_service_blob_proxy, model.signed_id(expires_in: expires_in), model.filename, options.merge(host: "winweb.b-cdn.net"))
      else
        route_for(:rails_service_blob, model.signed_id, model.filename, options)
      end
    else
      signed_blob_id = model.blob.signed_id(expires_in: expires_in)
      variation_key  = model.variation.key
      filename       = model.blob.filename

      if Rails.env.production?
        route_for(:rails_blob_representation_proxy, signed_blob_id, variation_key, filename, options.merge(host: "winweb.b-cdn.net"))
      else
        route_for(:rails_blob_representation, signed_blob_id, variation_key, filename, options)
      end
    end
  end

  if Rails.env.development?
    mount Lookbook::Engine, at: "/lookbook"
  end

  if Rails.env.production?
    constraints subdomain: false do
      get ":any", to: redirect(subdomain: "www", path: "/%{any}"), any: /.*/
      root to: redirect(subdomain: "www", path: "/"), as: :non_www_root
    end
  end

  namespace :admin do
    scope '/:website_id' do
      resource :dashboard, only: :show
      resources :sessions
      resources :pricing do
        patch :sort, on: :member
        resources :pricing_sections, module: :pricing
      end

      resources :forms do
        post :create_contact_form, on: :collection
      end

      resource :settings do
        resource :opening_hours, module: :settings
        resources :forms, module: :settings
        resources :languages, module: :settings
        resource :domain, module: :settings
        resource :social_networks, module: :settings
        resource :theme, module: :settings, only: [:show, :update] do
          patch :load_preset
        end
      end

      resources :reservations do
        member do
          get :confirm_form
          get :cancel_form
          patch :confirm
          patch :cancel
        end
      end

      resources :services do
        post ":id", on: :collection, to: "services#create"
      end

      namespace :content do
        concern :has_blocks do
          resources :blocks do
            patch :hide, on: :member
            patch :sort, on: :member
            post :create_reference, on: :collection

            resources :block_controls, module: :blocks, shallow: true do
              patch :sort, on: :member
            end

            member do
              patch :update_media_layer # Nová routa pro aktualizaci MediaLayer
            end
          end
        end

        resources :pricing_options, only: [:update]

        resources :media do
          patch :sort, on: :member
        end
      end
      scope :content, module: :content do
        resources :pages, except: [ :show ], concerns: :has_blocks do
          patch :sort, on: :member
          patch :toggle_visibility, on: :member
          get :search, on: :collection
        end
        resources :blocks
      end
    end
  end

  resources :webhooks, only: [] do
    post ":event", on: :collection, to: "webhooks#create"
  end

  resources :reservations do
    get :times, on: :collection
    get :dates, on: :collection
  end

  resources :inbox_messages

  get "(:locale)", to: "pages#homepage", as: :homepage, constraints: { locale: %w[cs en sk pl] }
  get "(:locale)/:slug", to: "pages#show", as: :page, constraints: { locale: %w[cs en sk pl], slug: %r{[^/]+} }

  # Defines the root path route ("/")
  root "pages#homepage"
end
