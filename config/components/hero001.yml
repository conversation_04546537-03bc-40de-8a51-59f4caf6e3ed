default:
  options:
    name: hero001
    theme: white
    layout: rows
    padding: "sm:py-8"
    outer_padding: "sm:py-0"
    container: "container-full"
    inner_container: "container-lg"
    gap_y: "gap-y-4"
    alignment: "center"
  media:
    inline_items_count: 3
    posts_limit: 3
    position: center
    type: gallery
    gap: 0
    media_items:
      - image_url: "https://images.unsplash.com/photo-1490645935967-10de6ba17061?q=80&w=3506&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
      - image_url: "https://images.unsplash.com/photo-1504674900247-0877df9cc836?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
      - image_url: "https://images.unsplash.com/photo-1555396273-367ea4eb4db5?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
  controls:
    - type: "BlockControls::Heading"
      position: 1
      text: "Full Screen Hero Banner"
      options:
        heading_type: "h1"
        pre_header: "Welcome to"
    - type: "BlockControls::Paragraph"
      position: 2
      text: "This full-screen hero banner showcases beautiful imagery with centered content. Perfect for creating a strong first impression on your website visitors."
    - type: "BlockControls::Button"
      position: 3
      options:
        primary_button_text: "Learn More"
        primary_button_link_type: "link"
        primary_button_link: "#"
        secondary_button_text: "Contact Us"
        secondary_button_link: "#"
        secondary_button_link_type: "link"
