default:
  options:
    name: "hero005" # Původní název bloku
    outer_container_layer:
      theme: "light"              # Z původního options.accent: "light" (předpokládáme mapování accent na theme)
      padding_y: "sm:py-0"        # Z původního options.outer_padding
      padding_x: "sm:px-4"        # Rozumný výchozí padding X, cílová struktura má často specifický
      container: "container-full" # Z původního options.container
    inner_container_layer:
      theme: "light"              # Z původního options.accent: "light"
      padding_y: "sm:py-0"        # Z původního options.padding
      padding_x: "sm:px-4"        # Rozumný výchozí padding X
      container: "container-full" # Z původního options.inner_container
    content_layer:
      theme: "light"              # Z původního options.accent: "light"
      padding_y: "py-4"           # Výchozí Y padding pro obsah (c<PERSON>lov<PERSON> struktura má často specifický)
      padding_x: "px-4"           # Výchozí X padding pro obsah
      container: "max-w-prose"    # Běžný výchozí kontejner pro textový obsah, pokud není specifikováno jinak
      gap_y: "gap-y-2"            # Z původního options.gap_y
      alignment: "left"           # Z původního options.alignment
    media_layer:
      theme: "light"              # Z původního options.accent: "light"
      padding_y: "py-0"           # Výchozí Y padding pro média
      padding_x: "px-0"           # Výchozí X padding pro média
      gap_y: "gap-y-0"            # Výchozí vertikální mezera v media vrstvě (odlišná od `gap` mezi položkami)
      alignment: "center"         # Běžné výchozí zarovnání pro mediální blok
      inline_items_count: 1       # Z původního media.inline_items_count
      posts_limit: 1              # Z původního media.posts_limit
      position: "left"            # Z původního media.position (pozice média vůči obsahu)
      gap: 0                      # Z původního media.gap (mezera mezi mediálními položkami)
      type: "gallery"             # Z původního media.type
      layout: "grid"              # Běžný výchozí layout pro typ "gallery", cílová struktura má "strip"
  media_items: # Přesunuto na nejvyšší úroveň
    - image_url: "https://picsum.photos/865/620"
    - image_url: "https://picsum.photos/865/620"
  controls: # Zůstává na nejvyšší úrovni, obsah zkopírován
    - type: "BlockControls::Heading"
      position: 1
      text: "Hero 006 - Heading" # Ponecháno z původního, i když název bloku je hero005
      options:
        heading_type: "h2"
        pre_header: "Hello"
    - type: "BlockControls::Paragraph"
      position: 2
      text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc a lectus nisi. Vestibulum euismod feugiat neque a ullamcorper. Aliquam erat volutpat. Proin finibus vestibulum metus, vel sodales nibh efficitur ornare. Donec lacinia sapien posuere eros blandit, quis volutpat leo rhoncus. Nunc quis finibus diam, et scelerisque libero."
    - type: "BlockControls::Button"
      position: 3
      options:
        primary_button_text: "Zjistit více"
        primary_button_link_type: "link"
        primary_button_link: "#"
        secondary_button_text: "Kontaktujte nás"
        secondary_button_link: "#"
        secondary_button_link_type: "link"