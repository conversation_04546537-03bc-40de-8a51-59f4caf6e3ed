default:
  options:
    name: "Instagram Feed Gallery" # Název bloku
    outer_container_layer:
      theme: "light" # Ovlivní base barvy, pokud je CSS nastaveno
      padding_y: "py-16 lg:py-24" # Přesně dle HTML designu
      # padding_x: "" # Není v HTML pro <section>
      # container: "" # Není v HTML pro <section>
    inner_container_layer:
      theme: "light"
      # padding_y: "" # Není v HTML pro tento div
      padding_x: "px-4" # Přesně dle HTML designu
      container: "container" # Přesně dle HTML designu (`container mx-auto px-4`)
    content_layer:
      theme: "light"
      # padding_y, padding_x: nejsou přímo na obalujícím divu hlavičky
      container: "max-w-2xl" # Pro <p class="... max-w-2xl ...">; HTML design
      # gap_y: "gap-y-N" # Pro vertikální mezery mezi controls, pokud by BaseComponent podporoval iteraci a gap
      alignment: "center" # HTML design je centrován
    media_layer:
      theme: "light"
      # padding_y, padding_x: nejsou na obalujícím divu mřížky
      gap: 6 # Odpovídá `gap-6` v HTML pro mřížku
      # inline_items_count: 5 # Tento klíč je pro gallery001, zde je layout pevný.
      posts_limit: 10 # Kolik příspěvků maximálně zobrazit (např.)
      # position, type, layout: nejsou pro gallery002 přímo konfigurovatelné tímto způsobem

  media_items:
    - image_url: "https://picsum.photos/seed/insta_a/400/400"
      title: "Ranní inspirace"
      published_at: "28. května 2025"
      caption: "Začněte den s námi! Denní dávka krásy a pozitivní energie přímo z našeho studia."
      url: "https://www.instagram.com/p/Cexample1/"
    - image_url: "https://picsum.photos/seed/insta_b/400/400"
      title: "Nové trendy"
      published_at: "27. května 2025"
      caption: "Sledujeme nejnovější trendy v beauty světě a přinášíme vám je jako první."
      url: "https://www.instagram.com/p/Cexample2/"
    - image_url: "https://picsum.photos/seed/insta_c/400/400"
      title: "Relaxace a péče"
      caption: "Dopřejte si chvilku pro sebe. Naše tipy na dokonalou relaxaci a péči."
      url: "https://www.instagram.com/p/Cexample3/"
    - image_url: "https://picsum.photos/seed/insta_d/400/400"
      title: "Ze zákulisí"
      published_at: "25. května 2025"
      caption: "Nahlédněte s námi do zákulisí příprav nových kolekcí a služeb."
      url: "https://www.instagram.com/p/Cexample4/"
    - image_url: "https://picsum.photos/seed/insta_e/400/400"
      title: "Váš názor nás zajímá"
      caption: "Co byste si přáli vidět více? Dejte nám vědět do komentářů!"
      url: "https://www.instagram.com/p/Cexample5/"
    # ... přidejte dalších 5 položek pro celkem 10 (pokud posts_limit: 10)

  controls:
    - type: "BlockControls::Heading"
      text: "Sledujte nás na Instagramu" # Komponenta automaticky zvýrazní "Instagramu"
      options:
        heading_type: "h2" # Může být h1, h2, h3...
        pre_header: "" # Tento blok ho nepoužívá
    - type: "BlockControls::Paragraph"
      text: "Denní dávka inspirace, zákulisí a nejnovějších trendů z našeho beauty prostoru"
    - type: "BlockControls::Button" # Komponenta použije tato data pro odkaz
      options:
        primary_button_text: "Sledovat @oficina_2.0"
        primary_button_link: "https://www.instagram.com/oficina_2.0/" # Doplňte skutečný odkaz
        primary_button_link_type: "link" # Naznačuje, že se má otevřít jako odkaz (target _blank)
        # secondary_button_text: "" # Tento blok nepoužívá sekundární tlačítko
        # secondary_button_link: ""
        # secondary_button_link_type: ""