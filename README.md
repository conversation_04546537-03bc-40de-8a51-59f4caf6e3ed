ssh -i /Users/<USER>/wample root@157.180.78.162
f72c0e93b24c422434309cec8cf96485c6fc908c0aefa49d1141be89dd312d9c
56fb37eb179c61cfe345ddd7348669f6208a67d1


MediaCollection
name

rails g model MediaCollection website:belongs_to name:string



New DynamicMedia model

Media
- media_type_id
- data:jsonb

MediaCollection
- name
- media_id

MediaType
- name (Gallery, Reviews, Articles etc.)
has_many: media_fields

rails g model MediaType name:string

MediaField
- media_type_id
- field_key
- field_type (String, Content, Icon, Stars)
- required
- position

rails g model MediaField media_type:belongs_to field_key:string field_type:string position:integer required:boolean