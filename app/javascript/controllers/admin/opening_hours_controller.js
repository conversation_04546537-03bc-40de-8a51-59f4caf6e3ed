import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["checkbox", "timeInput"]

    connect() {
        this.checkboxTargets.forEach(checkbox => {
            this.update(checkbox)
            checkbox.addEventListener("click", () => this.update(checkbox))
        })
    }

    reconnect() {
        this.checkboxTargets.forEach(checkbox => {
            this.update(checkbox)
            checkbox.addEventListener("click", () => this.update(checkbox))
        })
    }

    validate() {
        if (this.checkboxTarget.checked && this.timeInputTargets[0].value === "" && this.timeInputTargets[1].value === "") {
            alert("ERROR")
        }
    }

    update(checkbox) {
        const timeInputs = checkbox.closest('.parent-class').querySelectorAll("input[type='time']")
        if (checkbox.checked) {
            timeInputs.forEach(input => input.removeAttribute("disabled"))
        } else {
            timeInputs.forEach(input => input.setAttribute("disabled", "true"))
        }
    }
}