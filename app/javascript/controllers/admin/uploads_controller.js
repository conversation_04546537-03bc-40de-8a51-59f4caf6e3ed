// app/javascript/controllers/uploads_controller.js
import { Controller } from "@hotwired/stimulus";
import { post } from "@rails/request.js";

export default class extends Controller {
    static targets = ["fileInput"]
    static values = { url: String, blockId: Number, responseKind: { type: String, default: 'json' } }

    upload(event) {
        const files = this.fileInputTarget.files;
        if (files.length === 0) {
            alert("Please select at least one file.");
            return;
        }

        // Loop through selected files and upload each one
        Array.from(files).forEach(file => this.uploadFile(file));
    }

    async uploadFile(file) {
        const formData = new FormData();
        formData.append("image", file);
        formData.append("block_id", this.blockIdValue);

        try {
            const response = await post(this.urlValue, {
                body: formData,
                contentType: false,
                responseKind: this.responseKindValue,
            });

            if (response.ok) {

            } else {

            }

        } catch (error) {
            console.error("Error during upload:", error);
            this.uploadStatusTarget.innerHTML += `<p>Error: ${error.message}</p>`;
        }
    }
}
