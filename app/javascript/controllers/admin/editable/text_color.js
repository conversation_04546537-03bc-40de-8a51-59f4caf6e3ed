import { Mark } from '@tiptap/core';

export const TextColor = Mark.create({
    name: 'textColor',

    addOptions() {
        return {
            // Seznam povolených tříd
            colors: ['text-accent-content', 'text-accent', 'text-instagram']
        }
    },

    addAttributes() {
        return {
            color: {
                default: null,
                parseHTML: element => element.getAttribute('data-text-color'),
                renderHTML: attributes => {
                    if (!attributes.color) {
                        return {};
                    }
                    return {
                        'data-text-color': attributes.color,
                        // Nastavíme třídu podle vybrané barvy
                        class: attributes.color
                    }
                },
            },
        }
    },

    parseHTML() {
        return [
            {
                tag: 'span[data-text-color]',
            },
        ];
    },

    renderHTML({ HTMLAttributes }) {
        return ['span', HTMLAttributes, 0];
    },

    addCommands() {
        return {
            setColor: color => ({ commands }) => {
                if (!this.options.colors.includes(color)) {
                    // Pokud není barva povolená, p<PERSON><PERSON><PERSON> se neprovede
                    return false;
                }
                return commands.setMark(this.name, { color });
            },
            unsetColor: () => ({ commands }) => {
                return commands.unsetMark(this.name);
            },
        }
    },
});
