import { Controller } from "@hotwired/stimulus";
import { useDebounce } from "stimulus-use";
import { Editor } from "@tiptap/core";
import BubbleMenu from '@tiptap/extension-bubble-menu'
import TextStyle from "@tiptap/extension-text-style";
import Underline from "@tiptap/extension-underline";
import StarterKit from "@tiptap/starter-kit";
import Highlight from '@tiptap/extension-highlight'
import { TextColor } from "./text_color";

export default class extends Controller {
    static values = { key: String, text: String, previewElementId: String }
    static targets = ["content", "menu", "inputContent", "inputText", "colorMenu"]

    static debounces = [
        {
            name: 'handleEditorUpdate', // Název vaší metody, která obsahuje logiku
            wait: 1000 // Čas v milisekundách, po který se ček<PERSON> na nečinnost
        }
    ];

    connect() {
        useDebounce(this);
    }

    contentTargetConnected() {
        const previewElementId = this.previewElementIdValue

        this.colorMenuTarget.dataset.theme = this.element.dataset.id

        this.editor = new Editor({
            element: this.contentTarget,
            extensions: [
                TextColor,
                StarterKit,
                TextStyle,
                Highlight.configure({ multicolor: true }),
                Underline,
                BubbleMenu.configure({
                    element: this.menuTarget,
                }),
            ],
            content: this.textValue,
            onUpdate: ({ editor }) => {
                const previewElementId = this.previewElementIdValue;
                const previewEl = document.getElementById(previewElementId);

                console.log(previewElementId)
                if (previewEl) {
                    previewEl.innerHTML = editor.getHTML();
                }

                this.handleEditorUpdate({ editor });
            },
        });
    }

    handleEditorUpdate({ editor }) {
        if (this.hasInputContentTarget) {
            this.inputContentTarget.value = JSON.stringify(editor.getJSON());
        }

        if (this.hasInputTextTarget) {
            this.inputTextTarget.value = editor.getHTML();
        }
    }

    showColorMenu() {
        this.colorMenuTarget.classList.remove("hidden")

        if (this.timer) {
            clearTimeout(this.timer)
        }
    }

    hideColorMenu() {
       this.timer = setTimeout(() => {
            this.colorMenuTarget.classList.add("hidden")
        }, 300)
    }

    menuTargetConnected() {
        this.updateButtonStates();
    }

    disconnect() {
        if (this.editor) {
            this.editor.destroy();
            this.editor = null; // Dobrý zvyk pro uvolnění reference
        }
    }

    toggleBold() {
        this.editor.chain().focus().toggleBold().run();

        this.updateButtonStates();
    }

    toggleItalic() {
        this.editor.chain().focus().toggleItalic().run();

        this.updateButtonStates();
    }

    toggleHighlight(event) {
        const color = event.currentTarget.dataset.color;

        // Pokud je zvýraznění s danou barvou aktivní, odstraníme ho
        if (this.editor.isActive('highlight', { color })) {
            this.editor.chain().focus().unsetHighlight().run();
        } else {
            // Jinak nastavíme zvýraznění s požadovanou barvou
            this.editor.chain().focus().toggleHighlight({ color }).run();
        }

        this.updateButtonStates();
    }

    toggleStrike() {
        this.editor.chain().focus().toggleStrike().run();

        this.updateButtonStates();
    }

    toggleUnderline() {
        this.editor.chain().focus().toggleUnderline().run();

        this.updateButtonStates();
    }

    toggleColor(event) {
        const color = event.currentTarget.dataset.color;

        // Pokud je vybraná barva již aktivní, zavoláme unsetColor
        if (this.editor.isActive('textColor', { color })) {
            this.editor.chain().focus().unsetColor().run();
        } else {
            this.editor.chain().focus().setColor(color).run();
        }

        this.updateButtonStates();
    }

    toggleBulletList() {
        this.editor.chain().focus().toggleBulletList().run();
        this.updateButtonStates();
    }

    toggleOrderedList() {
        this.editor.chain().focus().toggleOrderedList().run();
        this.updateButtonStates();
    }

    updateButtonStates() {
        const isActive = (mark, options = {}) => this.editor.isActive(mark, options);

        this.element.querySelectorAll('.toggle-button').forEach(button => {
            const action = button.dataset.action;

            // Pokud se jedná o tlačítko pro zvýraznění (má data-action toggleHighlight)
            if (button.dataset.action === "click->tiptap#toggleHighlight") {
                const color = button.dataset.color;
                if (isActive('highlight', { color })) {
                    button.classList.add('bg-gray-200');
                    button.classList.remove('bg-white', 'hover:bg-gray-200');
                } else {
                    button.classList.add('bg-white', 'hover:bg-gray-200');
                    button.classList.remove('bg-gray-200');
                }
            } else if (button.dataset.color) {
                // Kontrola pro tlačítka, která ovládají barvu textu
                const color = button.dataset.color;
                if (isActive('textColor', { color })) {
                    button.classList.add('bg-gray-200');
                    button.classList.remove('bg-white', 'hover:bg-gray-200');
                } else {
                    button.classList.add('bg-white', 'hover:bg-gray-200');
                    button.classList.remove('bg-gray-200');
                }
            } else if (action === "click->tiptap#toggleBulletList") {
                if (this.editor.isActive('bulletList')) {
                    button.classList.add('bg-gray-200');
                    button.classList.remove('bg-white', 'hover:bg-gray-200');
                } else {
                    button.classList.add('bg-white', 'hover:bg-gray-200');
                    button.classList.remove('bg-gray-200');
                }
            } else if (action === "click->tiptap#toggleOrderedList") {
                if (this.editor.isActive('orderedList')) {
                    button.classList.add('bg-gray-200');
                    button.classList.remove('bg-white', 'hover:bg-gray-200');
                } else {
                    button.classList.add('bg-white', 'hover:bg-gray-200');
                    button.classList.remove('bg-gray-200');
                }
            } else {
                // Kontrola pro ostatní tlačítka (např. bold, italic apod.)
                const action = button.dataset.action;
                if (action) {
                    const [ , actionType] = action.split('#');
                    if (actionType) {
                        const mark = actionType.replace('toggle', '').toLowerCase();
                        if (isActive(mark)) {
                            button.classList.add('bg-gray-200');
                            button.classList.remove('bg-white', 'hover:bg-gray-200');
                        } else {
                            button.classList.add('bg-white', 'hover:bg-gray-200');
                            button.classList.remove('bg-gray-200');
                        }
                    }
                }
            }
        });
    }


}