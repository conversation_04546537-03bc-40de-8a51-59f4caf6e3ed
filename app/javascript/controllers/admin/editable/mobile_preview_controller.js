import {Controller} from "@hotwired/stimulus";

export default class extends Controller {
    static targets = ["previewButton", "iframeContainer", "mainContent"];

    connect() {
        this.isMobilePreview = false;
        this.iframe = null;
    }

    togglePreview() {
        this.isMobilePreview = !this.isMobilePreview;

        if (this.isMobilePreview) {
            this.showMobilePreview();
        } else {
            this.hideMobilePreview();
        }
    }

    showMobilePreview() {
        // Skrytí hlavního obsahu
        this.mainContentTarget.style.display = "none";

        // Vytvoření iframe pro mobilní náhled
        this.createIframe();
    }

    hideMobilePreview() {
        // Zobrazení hlavního obsahu
        this.mainContentTarget.style.display = "block";

        // Odstranění iframe
        this.removeIframe();
    }

    createIframe() {
        this.iframe = document.createElement('iframe');
        this.iframe.src = window.location.href; // Načte aktuální stránku
        this.iframe.style.width = "393px";  // Šířka mobilního zařízení
        this.iframe.style.height = "100vh"; // Výška mobilního zařízení
        this.iframe.style.border = "none";
        this.iframe.style.position = "absolute";
        this.iframe.style.top = "0";
        this.iframe.style.left = "50%";
        this.iframe.style.transform = "translateX(-50%)";

        // Nastavení události pro načtení iframe
        this.iframe.onload = () => {

            // Přidání meta tagu do iframe dokumentu
            this.iframe.contentWindow.document.head.appendChild(meta);
        };

        // Přidání iframe do containeru
        this.iframeContainerTarget.appendChild(this.iframe);
    }

    removeIframe() {
        if (this.iframe) {
            this.iframe.remove();
            this.iframe = null;
        }
    }
}
