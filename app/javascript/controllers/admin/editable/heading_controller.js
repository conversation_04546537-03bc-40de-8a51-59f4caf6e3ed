import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
    static values = {
        text: String,
        preHeader: String,
        controlId: Number
    }

    onUpdatePreHeader(e) {
        const value = e.target.value;

        if (value) {
            document.getElementById(`control-${this.controlIdValue}-pre-header`).classList.remove('hidden');
        } else {
            document.getElementById(`control-${this.controlIdValue}-pre-header`).classList.add('hidden');
        }

        document.getElementById(`control-${this.controlIdValue}-pre-header`).innerText = e.target.value;
    }

    onUpdateHeading(e) {
        console.log(e.target.value);
        console.log(document.getElementById(`control-${this.controlIdValue}-heading`))
        document.getElementById(`control-${this.controlIdValue}-heading`).classList.remove('h1', 'h2', 'h3');
        document.getElementById(`control-${this.controlIdValue}-heading`).classList.add(e.target.value);
    }
}
