// app/javascript/controllers/live_update_controller.js
import { Controller } from "@hotwired/stimulus"
import { FetchRequest } from '@rails/request.js' // Ujistěte se, že máte @rails/request.js
import { debounce } from "throttle-debounce"     // Volitelné pro debounce

export default class extends Controller {
    static values = {
        url: String,          // URL pro dedikovanou update akci (např. update_media_layer)
        targetFrame: String,  // ID turbo-frame na hlavní stránce, který se má aktualizovat
        delay: { type: Number, default: 300 } // Defaultní debounce delay v ms
    }

    static targets = ["field"] // Pokud chcete explicitně označit pole, které se má odeslat

    connect() {

    }

    // Tuto metodu zavolá `data-action="change->live-update#sendOnChange"` nebo `slider:slid->live-update#sendOnChange`
    async sendOnChange(event) {
        const inputElement = event.target.closest('[name]') || this.findAssociatedInput(event.target) || event.target;

        if (!inputElement || !inputElement.name) {
            console.error("LiveUpdate: Nemohu najít input element nebo jeho jméno.", event.target);
            return;
        }

        const fieldName = inputElement.name;  // např. "block[options][media_layer_attributes][layout]"
        const fieldValue = inputElement.value;

        // Připravíme FormData pro odeslání. Rails toto umí správně zpracovat na vnořené parametry.
        const formData = new FormData();

        // Přidáme CSRF token
        const csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content");
        formData.append('authenticity_token', csrfToken);

        // Přidáme informaci o cílovém turbo-frame pro odpověď serveru
        if (this.hasTargetFrameValue) {
            formData.append('target_turbo_frame', this.targetFrameValue);
        }
        // Přidáme informaci o formulářovém rámci pro případnou chybu
        // (ID fieldsetu nebo divu obalujícího media_layer_attributes)
        const formSectionId = this.element.closest('fieldset')?.id || this.element.id || `media_layer_fields_for_block_${this.element.dataset.optionsIdValue}`;
        formData.append('media_layer_form_frame', formSectionId);


        // Přidáme změněné pole. Rails by měl z `fieldName` správně sestavit vnořenou strukturu.
        formData.append(fieldName, fieldValue);

        // Pokud chcete odeslat všechna pole z `media_layer_attributes` fieldsetu:
        // const fieldset = inputElement.closest('fieldset'); // Nebo jiný obalující element
        // if (fieldset) {
        //   const inputs = fieldset.querySelectorAll('input, select, textarea');
        //   inputs.forEach(input => {
        //     if (input.name && input.name.includes('[media_layer_attributes]')) {
        //       if ((input.type === 'checkbox' || input.type === 'radio') && !input.checked) {
        //         // Pro unchecked checkbox/radio neděláme nic, FormData to řeší
        //         // Pokud byste potřebovali poslat "0" pro unchecked, muselo by se to řešit jinak.
        //       } else {
        //         formData.append(input.name, input.value);
        //       }
        //     }
        //   });
        // } else { // Fallback na odeslání jen jednoho pole, pokud fieldset nenalezen
        //   formData.append(fieldName, fieldValue);
        // }


        console.log("OK")
        const request = new FetchRequest('patch', this.urlValue, {
            body: formData,
            responseKind: 'turbo-stream' // Očekáváme turbo-stream odpověď
        });

        const response = await request.perform();
        if (response.ok) {
            // Turbo stream se zpracuje automaticky
        } else {
            console.error("Chyba při částečné aktualizaci MediaLayer", await response.textBody);
            // Zde můžete zobrazit uživateli chybovou hlášku
        }
    }

    findAssociatedInput(eventTarget) {
        // Pro slidery, kde event.target je div a input je data-slider-target="input"
        if (eventTarget.dataset.sliderTarget === "handler") { // Nebo jak identifikujete slider
            const sliderRoot = eventTarget.closest('[data-controller~="slider"]');
            return sliderRoot ? sliderRoot.querySelector('[data-slider-target="input"]') : null;
        }
        // Pro dropdown, pokud se event spouští na labelu
        if (eventTarget.tagName === 'LABEL') {
            return eventTarget.querySelector('input[type="radio"], input[type="checkbox"]');
        }
        return null;
    }
}