import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
    static values = {
        controlId: Number
    }

    connect() {
    }

    onUpdatePrimaryButtonText(e) {
        const element = document.getElementById(`control-${this.controlIdValue}-primary-button-text`)

        if (e.target.value === '') {
            element.classList.add("hidden")
            return;
        }

        element.classList.remove("hidden")
        element.innerText = e.target.value;
    }

    onUpdateSecondaryButtonText(e) {
        const element = document.getElementById(`control-${this.controlIdValue}-secondary-button-text`)

        if (e.target.value === '') {
            element.classList.add("hidden")
            return;
        }

        element.classList.remove("hidden")
        element.innerText = e.target.value;
    }
}
