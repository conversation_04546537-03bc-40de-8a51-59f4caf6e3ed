import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
    static values = {
        controlId: Number
    }

    static targets = ["input"]

    onUpdateText(text) {
        document.getElementById(`control-${this.controlIdValue}-text`).innerHTML = text;
        this.inputTarget.value = text
    }

    onUpdateFontSize(e) {
        document.getElementById(`control-${this.controlIdValue}-text`).classList.remove('text-xs', 'text-sm', 'text-base', 'text-lg', 'text-xl', 'text-2xl', 'text-3xl', 'text-4xl', 'text-5xl', 'text-6xl');
        document.getElementById(`control-${this.controlIdValue}-text`).classList.add(e.target.value);
    }

    onUpdateAlignment(e) {
        document.getElementById(`control-${this.controlIdValue}-text`).classList.remove('text-left', 'text-center', 'text-right', 'text-justify');
        document.getElementById(`control-${this.controlIdValue}-text`).classList.add(e.target.value);
    }
}
