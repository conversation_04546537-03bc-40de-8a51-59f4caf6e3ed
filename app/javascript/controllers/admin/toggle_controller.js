import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ['toggleable', 'radio']
    static values = { open: String }

    connect() {
        this.init()
    }

    reconnect() {
        this.init()
    }

    init() {
        this.toggleClass = this.data.get('class') || 'hidden'
        this.showContentBasedOnRadio()
    }

    showContentBasedOnRadio() {
        this.toggleableTargets.forEach(target => {
            const targetId = target.dataset.toggleableId
            const isVisible = this.openValue === targetId
            target.classList.toggle(this.toggleClass, !isVisible)
        })
    }

    change(event) {
        this.openValue = event.target.value
    }

    openValueChanged() {
        this.showContentBasedOnRadio()
    }
}