import { Dropdown } from "tailwindcss-stimulus-components"

export default class extends Dropdown {
    static targets = ["button", "input", "item", "menu"];
    connect() {
        super.connect();

        this.init()
    }

    reconnect() {
        this.init()
    }

    init() {
        if (this.inputTargets.length > 0) {
            const checked = this.inputTargets.find((input) => input.checked);

            if (checked) {
                // Handle theme selection
                this.element.querySelectorAll(".selected-" + checked.value).forEach((item) => {
                    item.classList.remove("hidden");
                });

                // Handle background color selection
                this.element.querySelectorAll(".selected-bg-" + checked.value).forEach((item) => {
                    item.classList.remove("hidden");
                });
            }
        }
    }

    change() {
        this.inputTargets.forEach((input) => {
            // Handle theme selection
            const selectedThemeItems = this.element.querySelectorAll(".selected-" + input.value);
            if (input.checked) {
                selectedThemeItems.forEach((item) => {
                    item.classList.remove("hidden");
                });
            } else {
                selectedThemeItems.forEach((item) => {
                    item.classList.add("hidden");
                });
            }

            // Handle background color selection
            const selectedBgItems = this.element.querySelectorAll(".selected-bg-" + input.value);
            if (input.checked) {
                selectedBgItems.forEach((item) => {
                    item.classList.remove("hidden");
                });
            } else {
                selectedBgItems.forEach((item) => {
                    item.classList.add("hidden");
                });
            }
        });
    }
}
