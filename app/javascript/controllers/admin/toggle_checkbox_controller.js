import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["button", "checkbox", "toggleSpan", "iconOff", "iconOn"];

  connect() {
    this.updateButtonState();
  }

  toggle(event) {
    event.preventDefault();
    this.checkboxTarget.checked = !this.checkboxTarget.checked;
    this.updateButtonState();
  }

  updateButtonState() {
    const isChecked = this.checkboxTarget.checked;
    this.buttonTarget.setAttribute("aria-checked", isChecked);

    if (isChecked) {
      this.buttonTarget.classList.remove("bg-gray-200");
      this.buttonTarget.classList.add("bg-lime-600");
      this.toggleSpanTarget.classList.remove("translate-x-0");
      this.toggleSpanTarget.classList.add("translate-x-5");
      this.iconOffTarget.classList.add("opacity-0");
      this.iconOffTarget.classList.remove("opacity-100");
      this.iconOnTarget.classList.remove("opacity-0");
      this.iconOnTarget.classList.add("opacity-100");
    } else {
      this.buttonTarget.classList.remove("bg-lime-600");
      this.buttonTarget.classList.add("bg-gray-200");
      this.toggleSpanTarget.classList.remove("translate-x-5");
      this.toggleSpanTarget.classList.add("translate-x-0");
      this.iconOffTarget.classList.remove("opacity-0");
      this.iconOffTarget.classList.add("opacity-100");
      this.iconOnTarget.classList.add("opacity-0");
      this.iconOnTarget.classList.remove("opacity-100");
    }
  }
}
