import { application } from "../application"

import HwCombobox<PERSON>ontroller from "@josefarias/hotwire_combobox"

import Notification from '@stimulus-components/notification'
import { Tabs } from "tailwindcss-stimulus-components"
import ColorPicker from './color_picker_controller'
import RailsNestedForm from '@stimulus-components/rails-nested-form'
import Toggle from './toggle_controller'
import Sortable from "./sortable_controller"
import CreateMenuController from './create_menu_controller'
import ToggleCheckboxController from './toggle_checkbox_controller'
import OpeningHoursController from './opening_hours_controller'
import DropdownController from './dropdown_controller'
import SliderController from './slider_controller'
import HeadingController from "./editable/heading_controller";
import ParagraphController from "./editable/paragraph_controller";
import ButtonsController from "./editable/buttons_controller";
import BlockController from "./editable/block_controller";
import IconPicker<PERSON>ontroller from "./editable/icon_picker_controller";
import Splide<PERSON><PERSON>roller from '../splide_controller'
import Tip<PERSON><PERSON><PERSON>roller  from "./tippy_controller";
import Uploads<PERSON>ontroller from "./uploads_controller";
import MobilePreviewController from "./editable/mobile_preview_controller";
import TipTapController from "./editable/tiptap_controller";
import FormController from "./editable/form_controller";
import MediaLayerLiveUpdateController from "./editable/media_layer_live_update_controller";

application.register('notification', Notification)
application.register('tabs', Tabs)
application.register('dropdown', DropdownController)
application.register('color-picker', ColorPicker)
application.register('nested-form', RailsNestedForm)
application.register('sortable', Sortable)
application.register('toggle', Toggle)
application.register('create-menu', CreateMenuController)
application.register('toggle-checkbox', ToggleCheckboxController)
application.register('opening-hours', OpeningHoursController)
application.register("hw-combobox", HwComboboxController)
application.register("slider", SliderController)
application.register("heading", HeadingController)
application.register("paragraph", ParagraphController)
application.register("buttons", ButtonsController)
application.register("block", BlockController)
application.register('splide', SplideController)
application.register('tippy', TippyController)
application.register('uploads', UploadsController)
application.register('icon-picker', IconPickerController)
application.register('mobile-preview', MobilePreviewController)
application.register('tiptap', TipTapController)
application.register('form', FormController)
application.register('media-layer-live-update', MediaLayerLiveUpdateController)