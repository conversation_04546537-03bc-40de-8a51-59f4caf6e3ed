import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["type", "weeks", "validity", "validFrom", "validTo"]

    connect() {
        this.handleTypeChange()
    }

    reconnect() {
        this.handleTypeChange()
    }

    handleTypeChange() {
        switch (this.typeTarget.value) {
            case "daily":
                this.showWeeklyOptions();
                break;
            case "seasonal":
                this.showSeasonalOptions();
                break;
            default:
                this.hideAllOptions();
                break;
        }
    }

    showWeeklyOptions() {
        this.weeksTarget.classList.remove("hidden");
        this.validityTarget.classList.remove("hidden");
    }

    showSeasonalOptions() {
        this.weeksTarget.classList.add("hidden");
        this.validityTarget.classList.remove("hidden");
    }

    hideAllOptions() {
        this.weeksTarget.classList.add("hidden");
        this.validityTarget.classList.add("hidden");
    }

    setWeek(element) {
        const validFrom = element.target.dataset.validFrom;
        const validTo = element.target.dataset.validTo;

        this.validFromTarget.value = validFrom;
        this.validToTarget.value = validTo;
    }
}
