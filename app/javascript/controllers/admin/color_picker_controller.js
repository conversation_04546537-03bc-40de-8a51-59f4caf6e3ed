import ColorPicker from '@stimulus-components/color-picker'

export default class extends ColorPicker {
    static values = {
        previewElement: String,
    }

    connect() {
        super.connect()

        const element = this.element
        this.picker
            .on('change', this.onChange.bind(this))
    }

    onChange(color) {
        const hex = color.toHEXA().toString()

        if (this.previewElementValue) {
            const previewElement = document.querySelector(this.previewElementValue)
            if (previewElement) {
                previewElement.style.backgroundColor = hex
            }
        }
    }

    // Callback when the color is saved
    onSave(color) {
        super.onSave(color)
    }

    // You can override the components options with this getter.
    // Here are the default options.
    get componentOptions() {
        return {
            preview: true,
            hue: true,
            opacity: true,

            interaction: {
                input: true,
                clear: true,
                save: true,
            },
        }
    }

    // You can override the swatches with this getter.
    // Here are the default options.
    get swatches() {
        return [
            '#FFFFFF',
            '#F56565',
            '#ED8936',
            '#ECC94B',
            '#48BB78',
            '#38B2AC',
            '#4299E1',
            '#667EEA',
            '#9F7AEA',
            '#ED64A6',
        ]
    }
}