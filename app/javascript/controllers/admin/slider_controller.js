import { Controller } from "@hotwired/stimulus"
import * as noUiSlider from 'nouislider';

export default class extends Controller {
    static targets = ["handler", "input"]
    static values = { values: Array, current: String, element: String }

    connect() {
        const valuesArray = this.valuesValue;

        const slider = noUiSlider.create(this.handlerTarget, {
            start: [this.inputTarget.value],
            step: 1,
            connect: [true, false],
            range: {
                'min': parseInt(this.inputTarget.getAttribute('min')),
                'max': parseInt(this.inputTarget.getAttribute('max'))
            },
            format: {
                to: function (value) {
                    return valuesArray[Math.round(value)];
                },
                from: function (value) {
                    return valuesArray.indexOf(value);
                }
            },
            tooltips: {
                to: function (value) {
                    return Math.round(value);
                },
                from: function (value) {
                    return Math.round(value);
                }
            }
        });

        slider.on('update', (values, handle) => {
            const blockElement = document.getElementById(this.elementValue);
            const value = values[handle];

            if (blockElement !== null) {
                blockElement.classList.remove(this.currentValue);
                blockElement.classList.add(value);
                this.currentValue = value;
            }

            this.inputTarget.value = value;
        });

        slider.on('start', (values, handle) => {
            const blockElement = document.getElementById(this.elementValue);
            if (blockElement !== null) {
                blockElement.classList.add(
                    'outline',
                    'outline-4',
                    'outline-orange-500',
                    'shadow-orange-500/40',
                    'shadow-md'
                );
            }
        });

        slider.on('end', (values, handle) => {
            const blockElement = document.getElementById(this.elementValue);
            if (blockElement !== null) {
                blockElement.classList.remove(
                    'outline',
                    'outline-4',
                    'outline-orange-500',
                    'shadow-orange-500/40',
                    'shadow-md'
                );
            }
        });


        slider.on('change', (values, handle) => {
            this.inputTarget.value = values[handle];
        });
    }
}
