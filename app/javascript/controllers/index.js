// This file is auto-generated by ./bin/rails stimulus:manifest:update
// Run that command whenever you add a new controller or create them with
// ./bin/rails generate stimulus controllerName

import { application } from "./application"
import { Dropdown, Tabs } from "tailwindcss-stimulus-components";
import StickyHeaderController  from "./sticky_header_controller";
import AirDatePickerController from "./date_picker_controller";
import MapController from "./map_controller";
import ReservationController from "./reservation_controller";
import ScrollTo from '@stimulus-components/scroll-to'
import SplideController from './splide_controller'

application.register("dropdown", Dropdown)
application.register('tabs', Tabs)
application.register("sticky-header", StickyHeaderController)
application.register("air-datepicker", AirDatePickerController)
application.register("map", MapController)
application.register("reservation", ReservationController)
application.register('scroll-to', ScrollTo)
application.register('splide', SplideController)