import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    connect() {
        this.handleScroll = this.handleScroll.bind(this);
        window.addEventListener('scroll', this.handleScroll);
    }

    disconnect() {
        window.removeEventListener('scroll', this.handleScroll);
    }

    handleScroll() {
        scrollY = this.element.offsetHeight

        if (window.pageYOffset >= scrollY) {
            this.element.style.backgroundColor = this.element.dataset.background.slice(0, 7)
        } else {
            this.element.style.backgroundColor = this.element.dataset.background;
        }
    }
}