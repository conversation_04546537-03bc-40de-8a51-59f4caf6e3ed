import { Controller } from "@hotwired/stimulus";
import AirDatepicker from 'air-datepicker';
import { get } from '@rails/request.js';

export default class extends Controller {
    static targets = ["date", "pricingItem", "times", "pricing", "emptyPricingAlert", "emptyDateAlert"];
    static values = { selectedDate: String };

    connect() {
        this.availableDays = [];
    }

    async onUpdatePricing() {
        const duration = this.totalDuration();

        this.timesTarget.innerHTML = '';
        this.emptyDateAlertTarget.classList.remove('hidden');

        if (duration === 0) {
            this.pricingTarget.classList.add('hidden');
            this.emptyPricingAlertTarget.classList.remove('hidden');
            this.datePicker.destroy();
            return;
        }

        this.emptyPricingAlertTarget.classList.add('hidden');
        this.pricingTarget.classList.remove('hidden');

        this.initializeCalendar();
    }

    formatDateToYYYYMMDD(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
    }

    async initializeCalendar() {
        if (this.datePicker) {
            this.datePicker.destroy();
        }

        const minDate = this.calculateMinDate();
        const maxDate = this.calculateMaxDate(minDate, 60);

        // Convert dates to ISO 8601 format (without milliseconds)
        const startDateISO = minDate.toISOString().split('.')[0] + 'Z';
        const endDateISO = maxDate.toISOString().split('.')[0] + 'Z';

        const url = `/reservations/dates?start_date=${encodeURIComponent(startDateISO)}&end_date=${encodeURIComponent(endDateISO)}&year=2024&duration=${this.totalDuration()}`;

        // Assuming `get` returns an instance of `FetchResponse2` that wraps the native response
        const fetchResponse = await get(url, { responseKind: 'json' });

        // Access the raw Response object
        const response = fetchResponse.response;

        if (response.ok) {
            const data = await response.json();

            const unavailableDays = data
                .filter(item => !item.available) // Filter where available is false
                .map(item => item.day.split('T')[0]); // Extract the date part

            const that = this;

            this.datePicker = new AirDatepicker(this.dateTarget, {
                inline: true,
                locale: this.localeCs(),
                minDate: minDate,
                maxDate: maxDate,
                onSelect: this.onDateSelect.bind(this),
                onChangeViewDate({ month }) {

                },
                onRenderCell({date, cellType}) {
                    if (cellType === 'day') {
                        const formattedDate = that.formatDateToYYYYMMDD(date);
                        if (unavailableDays.includes(formattedDate)) {
                            const dayNumber = date.getDate();
                            return {
                                html: `<span class="unavailable-day">${dayNumber}</span>`,
                                disabled: true
                            };
                        }
                    }
                }
            });
        }
    }

    async fetchAvailableTimeSlots(formattedDate) {
        if (!formattedDate) return;

        try {
            const url = this.buildUrl(formattedDate, this.totalDuration());
            await get(url, { responseKind: 'turbo-stream' });
        } catch (error) {
            this.handleError(error);
        }
    }

    calculateMinDate(month) {
        const now = new Date();
        return now.getHours() > 14 ? this.addDays(now, 1) : now;
    }

    calculateMaxDate(minDate, days) {
        return this.addDays(minDate, days);
    }

    async onDateSelect({ formattedDate }) {
        this.selectedDateValue = formattedDate;
        this.emptyDateAlertTarget.classList.add('hidden');
        try {
            await this.fetchAvailableTimeSlots(formattedDate);
        } catch (error) {
            this.handleError(error);
        }
    }

    buildUrl(date, duration) {
        return `/reservations/times?date=${date}&duration=${duration}`;
    }

    addDays(date, days) {
        return new Date(date.getTime() + days * 24 * 60 * 60 * 1000);
    }

    totalDuration() {
        return this.pricingItemTargets.reduce((acc, item) =>
                item.checked ? acc + parseInt(item.dataset.duration) : acc
            , 0);
    }

    localeCs() {
        return {
            days: ['Neděle', 'Pondělí', 'Úterý', 'Středa', 'Čtvrtek', 'Pátek', 'Sobota'],
            daysShort: ['Ne', 'Po', 'Út', 'St', 'Čt', 'Pá', 'So'],
            daysMin: ['Ne', 'Po', 'Út', 'St', 'Čt', 'Pá', 'So'],
            months: ['Leden', 'Únor', 'Březen', 'Duben', 'Květen', 'Červen', 'Červenec', 'Srpen', 'Září', 'Říjen', 'Listopad', 'Prosinec'],
            monthsShort: ['Led', 'Úno', 'Bře', 'Dub', 'Kvě', 'Čvn', 'Čvc', 'Srp', 'Zář', 'Říj', 'Lis', 'Pro'],
            today: 'Dnes',
            clear: 'Vymazat',
            dateFormat: 'dd.MM.yyyy',
            timeFormat: 'HH:mm',
            firstDay: 1
        };
    }

    handleError(error) {
        console.error("An error occurred:", error);
        // Optional: display a user-friendly message or take some action
        alert("Something went wrong while fetching data. Please try again later.");
    }
}
