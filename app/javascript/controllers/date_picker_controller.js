import { Controller } from "@hotwired/stimulus"

import AirDatepicker from 'air-datepicker';

export default class extends Controller {
    static values = { options: Object }
    connect() {
        const localeCs = {
            days: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>lí', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>t<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
            daysShort: ['Ne', '<PERSON>', 'Út', 'St', 'Čt', 'P<PERSON>', 'So'],
            daysMin: ['Ne', 'Po', 'Út', 'St', 'Čt', 'P<PERSON>', 'So'],
            months: ['<PERSON>en', '<PERSON>nor', 'B<PERSON>ezen', '<PERSON>en', 'Kv<PERSON><PERSON>', 'Červen', 'Červenec', 'Sr<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Říjen', 'Listopad', 'Prosinec'],
            monthsShort: ['Led', 'Úno', '<PERSON><PERSON>e', 'Dub', 'Kv<PERSON>', 'Čvn', 'Čvc', 'Srp', '<PERSON><PERSON><PERSON>', 'Říj', 'Lis', 'Pro'],
            today: 'Dnes',
            clear: 'Vymazat',
            dateFormat: 'dd.MM.yyyy',
            timeFormat: 'HH:mm',
            firstDay: 1
        };

        const now = new Date();
        let minDate;

        if (now.getHours() > 14) {
            minDate = new Date(now.getTime() + (24 * 60 * 60 * 1000));
        } else {
            minDate = new Date(now.getTime());
        }

        if (this.optionsValue.onlyTime) {
            new AirDatepicker(this.element, {
                locale: localeCs,
                onlyTimepicker: true,
                timepicker: true,
                minHours: 10,
                maxHours: 22,
                minutesStep: 15,
            })
        } else {
            new AirDatepicker(this.element, {
                inline: true,
                locale: localeCs,
                minDate: minDate,
            })
        }
    }
}
