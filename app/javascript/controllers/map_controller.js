import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    connect() {
        this.observer = new IntersectionObserver(this.loadMap.bind(this), {
            root: null,
            threshold: 0.1
        });

        this.observer.observe(this.element);
    }

    loadMap(entries, observer) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const iframe = document.createElement('iframe');
                iframe.width = "100%";
                iframe.height = "100%";
                iframe.src = this.element.dataset.mapUrl;
                iframe.style.height = "100%";

                this.element.appendChild(iframe);
                observer.unobserve(this.element);
            }
        });
    }

    disconnect() {
        this.observer.disconnect();
    }
}
