module BlocksHelper
  def render_block(block, resource, nested_blocks = nil)
    if block.name == "page_content"
      nested_blocks.each do |nested_block|
        concat(render("block", block_object: nested_block, resource: resource))
      end
    else
      concat(render(block.component))
    end
  end

  def render_empty_gallery
    div(class: "w-full h-full flex flex-col text-xl border flex py-12 rounded items-center justify-center text-gray-400") do
      svg(
        xmlns: "http://www.w3.org/2000/svg",
        fill: "none",
        viewbox: "0 0 24 24",
        stroke_width: "1.5",
        stroke: "currentColor",
        class: "size-6"
      ) do |s|
        s.path(
          stroke_linecap: "round",
          stroke_linejoin: "round",
          d:
            "m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
        )
      end
      p { "No Image" }
    end
  end
end