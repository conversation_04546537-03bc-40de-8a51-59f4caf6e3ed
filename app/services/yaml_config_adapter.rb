class YamlConfigAdapter < ConfigAdapter
  def initialize(identifier) # Přejmenováno z 'name' na 'identifier' pro jasnost
    # Načte konfiguraci pomocí vašeho BlockConfigLoader.
    # Očekává se, že vrátí hash s deep_symbolized klíči.
    @config = BlockConfigLoader.load(identifier)
    @identifier = identifier # Uložíme si původní identifikátor bloku
  end

  def name
    # Název bloku. Může pocházet z klíče :name v @config,
    # nebo použijeme identifikátor, pokud :name není přítomno.
    # Váš YAML má :name zanořené v :options, což je neobvyklé pro hlavní název bloku.
    # Pro hlavní název bloku je lepší @config[:name] (pokud existuje) nebo @identifier.
    @config[:name] || @identifier # Pokud váš YAML loader nastaví @config[:name] z nejvyšš<PERSON> úrovně
  end

  def options
    # Získáme sekci :options z @config.
    # Je dobré zde znovu zajistit deep_symbolize_keys, pokud si nejsme 100% jisti,
    # co vrací BlockConfigLoader.load, ale ideálně by to měl dělat už loader.
    yaml_options_section = (@config[:options] || {}).deep_symbolize_keys

    # Připravíme výsledný hash pro BlockObject
    final_options = {}

    # Mapování z YAML klíčů (např. :outer_container_layer)
    # na klíče očekávané BlockObjectem (např. :outer_container_layer_attributes)
    final_options[:outer_container_layer_attributes] = yaml_options_section[:outer_container_layer] || {}
    final_options[:inner_container_layer_attributes] = yaml_options_section[:inner_container_layer] || {}
    final_options[:content_layer_attributes] = yaml_options_section[:content_layer] || {}

    # Přidání ostatních "globálních" klíčů, které byly v YAML pod `options:`,
    # ale nejsou samotnými definicemi vrstev (např. :name ve vašem YAML).
    yaml_layer_keys_in_options = [:outer_container_layer, :inner_container_layer, :content_layer]
    global_keys_in_options = yaml_options_section.keys - yaml_layer_keys_in_options

    global_keys_in_options.each do |key|
      final_options[key] = yaml_options_section[key]
    end

    final_options
  end

  # Přejmenováno z 'controls' na 'controls_data'
  def controls_data
    # Očekáváme, že @config[:controls] je pole hashů.
    # Zajistíme symbolizaci klíčů i pro options uvnitř jednotlivých controls.
    (@config[:controls] || []).map do |control_hash|
      control_hash.is_a?(Hash) ? control_hash.deep_symbolize_keys : control_hash
    end
  end

  # Přejmenováno z 'media' na 'media_data'
  def media_data
    {
      type: @config[:options][:media_layer][:type],
      items_payload: @config[:media_items],
    }.deep_symbolize_keys
  end

  def pricing_id
    @config[:pricing_id] # Vrací nil, pokud není v @config
  end

  def pricing_options
    (@config[:pricing_options] || {}).deep_symbolize_keys
  end

  def background_image_attachment
    nil # YAML typicky neobsahuje přímé ActiveStorage přílohy
  end

  def background_image_mobile_attachment
    nil # YAML typicky neobsahuje přímé ActiveStorage přílohy
  end
end