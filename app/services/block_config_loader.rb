class BlockConfigLoader
  @cache = {}

  def self.load(block_name)
    @cache[block_name] ||= begin
                             file_path = Rails.root.join("config/components/#{block_name}.yml")
                             unless File.exist?(file_path)

                               raise "Konfigurační soubor pro blok '#{block_name}' nebyl nalezen na cestě: #{file_path}"
                             end

                             config_data = YAML.safe_load(
                               File.read(file_path),
                               permitted_classes: [Date, Time],
                               symbolize_names: true
                             )
                             config_data[:default] || {}
                           end
  end
end
