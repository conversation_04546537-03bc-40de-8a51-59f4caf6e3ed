module ControlRegistry
  CONTROL_CLASSES = {
    "BlockControls::Paragraph" => ParagraphControlPresenter,
    "BlockControls::Heading"   => HeadingControlPresenter, # Předpokládáme existenci
    "BlockControls::Button"    => ButtonControlPresenter, # Předpokládáme existenci
    "BadgeBlockControlObject"  => BadgeControlPresenter # Předpokládáme existenci
  }.freeze

  def self.class_for(control_type)
    CONTROL_CLASSES.fetch(control_type) do
      raise "Unknown control type: #{control_type}"
    end
  end
end