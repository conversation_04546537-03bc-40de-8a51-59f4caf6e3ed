require "open-uri"
class WebhookJob < ApplicationJob
  queue_as :default

  def perform(webhook_id)
    webhook = Webhook.find(webhook_id)

    processors = {
      "instagram" => Webhooks::InstagramProcessor,
      "google_maps" => Webhooks::GoogleMapsProcessor,
      "facebook" => Webhooks::FacebookProcessor
    }

    processor = processors[webhook.event_type]
    processor.process(webhook)

    webhook.update(processed_at: Time.now)
  end
end
