class HeadingControlPresenter < BasePresenter
  attr_reader :id, :pre_header, :text, :heading_type, :context

  def configure(config)
    @config = config
    @id = config[:id]
    @text = config[:text]
  end

  def id
    @config[:id]
  end

  def options
    { heading_type: heading_type, pre_header: pre_header }
  end

  def pre_header
    @config[:options][:pre_header] || @config.pre_header # @config.pre_header zde může být problematické
  end

  def heading_type
    @config[:options][:heading_type] || @config.heading_type # @config.heading_type zde mů<PERSON>e být problematické
  end

  def component
    BlockControls::HeadingControl.new(self)
  end

  def text
    @config[:text]
  end

  def type
    "BlockControls::Heading"
  end
end