class MediaItemPresenter # Nahraďte skutečným názvem va<PERSON><PERSON> třídy
  attr_reader :title, :image_url, :id, :image_attachment # A další atributy

  # Konstruktor může b<PERSON><PERSON> j<PERSON>
  def initialize(id: nil, title: nil, image_url: nil, image_attachment: nil, **other_attributes)
    @id = id
    @title = title
    @image_url = image_url
    @image_attachment = image_attachment # Pokud pracujete s ActiveStorage přílohami
    # @other_attributes = other_attributes # Pro další data
  end

  # Factory metoda pro vytvoření z datového hashe (např. z YAML nebo DB)
  def self.from_data(data_hash)
    # Zde můžete provést jakoukoliv potřebnou transformaci nebo validaci dat
    # Předpokládáme, že data_hash má symbolizované klíče
    new(**data_hash.symbolize_keys)
  end
end