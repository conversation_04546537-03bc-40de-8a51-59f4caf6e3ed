class MediaPresenter
  attr_reader :options, :items

  # item_class je t<PERSON><PERSON><PERSON>, kter<PERSON> se má použít pro vytvář<PERSON>í jednot<PERSON><PERSON><PERSON><PERSON>
  def initialize(options: {}, items_payload: [], item_class: MediaItemPresenter)
    @options = (options || {}).deep_symbolize_keys
    @items = (items_payload || []).map do |raw_item_data|
      item_class.from_data(raw_item_data) # Použijeme factory metodu z MediaItem
    end
  end

  def type
    @options[:type]
  end

  def inline_items_count
    @options[:inline_items_count] || 3
  end
end