class BlockPresenter
  attr_reader :id, :name, :raw_options, :media, :controls, :context, :pricing_id, :pricing_options, :background_image, :background_image_mobile
  attr_reader :outer_container_layer, :inner_container_layer, :content_layer

  def initialize(id:, name:, context:, options:, controls:, media:, pricing_id:, pricing_options:, background_image: nil, background_image_mobile: nil)
    @id = id
    @name = name
    @context = context
    @raw_options = options.deep_symbolize_keys
    @controls = controls
    @media = media
    @pricing_id = pricing_id
    @pricing_options = pricing_options&.deep_symbolize_keys || {} # Tak<PERSON> symbolizujeme
    @background_image = background_image
    @background_image_mobile = background_image_mobile
    @component = nil

    @outer_container_layer = Block::OuterContainerLayer.new(
      @raw_options[:outer_container_layer_attributes] || {} # <-- <PERSON><PERSON>ek<PERSON><PERSON><PERSON> k<PERSON> např. :outer_container_layer_attributes
    )
    @inner_container_layer = Block::InnerContainerLayer.new(
      @raw_options[:inner_container_layer_attributes] || {}
    )
    @content_layer = Block::ContentLayer.new(
      @raw_options[:content_layer_attributes] || {}
    )
  end

  def context=(new_context)
    @context ||= new_context
  end

  def media_alignment_class
    if @content_layer.media_alignment.present?
      @content_layer.media_alignment
    else
      "order-first"
    end
  end

  def media_position_class
    @media&.options&.fetch(:position, nil) == "right" ? "order-last" : "order-first"
  end

  def media_container_dom_id
    "block-#{id}-media-layer"
  end

  def component
    @component ||= Rails.application.config.x.components[@name.to_sym].constantize.new(self)
  end
end