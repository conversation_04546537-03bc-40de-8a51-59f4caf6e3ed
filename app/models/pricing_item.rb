# == Schema Information
#
# Table name: pricing_items
#
#  id                 :bigint           not null, primary key
#  description        :hstore           not null
#  duration           :integer
#  name               :hstore           not null
#  price              :decimal(10, 2)
#  price_eur          :decimal(10, 2)
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  pricing_section_id :bigint           not null
#
# Indexes
#
#  index_pricing_items_on_pricing_section_id  (pricing_section_id)
#
# Foreign Keys
#
#  fk_rails_...  (pricing_section_id => pricing_sections.id)
#
class PricingItem < ApplicationRecord
  extend Mobility
  translates :name, :description

  belongs_to :pricing_section

  validates :name_cs, presence: true

  EXCHANGE_RATE = 25.2

  def price_eur
    if read_attribute(:price_eur).present?
      read_attribute(:price_eur)
    else
      price ? pretty_price(price, EXCHANGE_RATE) : nil
    end
  end

  private

  def pretty_price(czk_price, exchange_rate = EXCHANGE_RATE)
    eur_price = czk_price / exchange_rate
    if eur_price < 1
      0.99
    elsif eur_price < 10
      if eur_price % 1 < 0.5
        (eur_price.floor + 0.49).round(2)
      else
        (eur_price.floor + 0.99).round(2)
      end
    else
      if eur_price % 1 < 0.5
        (eur_price.floor + 0.49).round(2)
      else
        (eur_price.floor + 0.99).round(2)
      end
    end
  end
end
