# == Schema Information
#
# Table name: media_collections
#
#  id              :bigint           not null, primary key
#  collection_type :string
#  name            :string
#  source          :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  website_id      :bigint           not null
#
# Indexes
#
#  index_media_collections_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#
class MediaCollection < ApplicationRecord
  acts_as_tenant(:website)

  SOURCE_INSTAGRAM = "Instagram".freeze

  has_many :media, -> { order(:position) }, dependent: :destroy, class_name: 'Media'
  belongs_to :media_type, optional: true

  accepts_nested_attributes_for :media, allow_destroy: true

  scope :with_media, -> { joins(:media).distinct }
  scope :instagram, -> { where(source: SOURCE_INSTAGRAM) }

  def to_combobox_display
    name
  end

  def instagram?
    source == SOURCE_INSTAGRAM
  end
end
