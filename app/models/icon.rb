# == Schema Information
#
# Table name: icons
#
#  id         :bigint           not null, primary key
#  name       :text
#  svg_html   :text
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
class Icon < ApplicationRecord
  def to_combobox_display
    name
  end

  def with(size:, color: nil)
    modified_svg = svg_html.sub(/<svg\s/, "<svg class=\"size-#{size}\" ")
    modified_svg = modified_svg.sub(/<svg\s/, "<svg style=\"fill: #{color};\" ") if color.present?
    modified_svg.html_safe
  end

  def self.with_size(icon, size)
    modified_svg = icon.svg_html.sub(/<svg\s/, "<svg class=\"size-#{size}\" ")
    modified_svg.html_safe
  end

  def self.by_name(name, size: 4)
    icon = find_by(name: name)
    return unless icon.present?

    with_size(icon, size)
  end
end
