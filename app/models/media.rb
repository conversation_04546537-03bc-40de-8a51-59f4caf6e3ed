# == Schema Information
#
# Table name: media
#
#  id                  :bigint           not null, primary key
#  content             :text             default("{}")
#  data                :jsonb
#  origin              :string
#  position            :integer          default(1), not null
#  published_at        :datetime
#  removed_at          :datetime
#  text                :text
#  title               :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  block_id            :bigint
#  icon_id             :bigint
#  image_id            :bigint
#  media_collection_id :bigint
#  media_type_id       :bigint
#  unique_id           :string
#  website_id          :bigint           not null
#
# Indexes
#
#  index_media_on_block_id             (block_id)
#  index_media_on_icon_id              (icon_id)
#  index_media_on_image_id             (image_id)
#  index_media_on_media_collection_id  (media_collection_id)
#  index_media_on_media_type_id        (media_type_id)
#  index_media_on_website_id           (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (block_id => blocks.id)
#  fk_rails_...  (icon_id => icons.id)
#  fk_rails_...  (image_id => images.id)
#  fk_rails_...  (media_collection_id => media_collections.id)
#  fk_rails_...  (media_collection_id => media_collections.id)
#  fk_rails_...  (media_type_id => media_types.id)
#  fk_rails_...  (website_id => websites.id)
#
class Media < ApplicationRecord
  include Removable

  MEDIA_TYPES = %w[Article Review Instagram Team].freeze

  acts_as_tenant(:website)

  scope :published, -> { where.not(published_at: nil) }

  has_one_attached :image do |attachable|
    attachable.variant :thumb, resize_to_fill: [80, 80]
  end

  store_accessor :data, :job_position, :media_url, :url, :caption

  belongs_to :icon, optional: true
  belongs_to :media_collection, optional: true
  belongs_to :media_type, optional: true
  belongs_to :block, optional: true

  # Validace podle typu média
  validate :validate_media_type_fields, if: -> { media_type.present? && !instagram? }

  def validate_media_type_fields
    return unless media_type

    media_type.required_fields.each do |field|
      next if field.field_key === "title"

      case field.field_type
      when 'string', 'text'
        errors.add(field.field_key.to_sym, "nemůže být prázdné") if data.blank? || data[field.field_key].blank?
      when 'content'
        errors.add(field.field_key.to_sym, "nemůže být prázdné") if content.blank?
      when 'image'
        errors.add(field.field_key.to_sym, "nemůže být prázdné") unless image.attached?
      when 'icon'
        errors.add(field.field_key.to_sym, "nemůže být prázdné") if icon_id.blank?
      when 'rating'
        errors.add(field.field_key.to_sym, "nemůže být prázdné") if data.blank? || data[field.field_key].blank?
      end
    end
  end

  positioned on: :media_collection_id

  def published?
    published_at.present? && published_at <= Time.current
  end

  def author_initials
    return "?" if author.nil?

    author.split(" ").map(&:first).join
  end

  def content_preview
    content.to_plain_text.truncate(100)
  end

  def anonymized_author_name
    # return name like Josef W.
    author.split(" ").first + " " + author.split(" ").last.first + "."
  end

  def to_combobox_display
    title
  end

  # Instagram-specific methods
  def instagram?
    media_collection&.instagram?
  end

  def instagram_post_id
    unique_id if instagram?
  end

  def instagram_caption
    caption if instagram?
  end

  def instagram_media_url
    media_url if instagram?
  end

  def instagram_url
    url if instagram?
  end
end
