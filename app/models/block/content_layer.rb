# app/models/block/inner_container_layer.rb
class Block
  class ContentLayer
    include ActiveModel::Model
    include ActiveModel::Attributes

    attribute :theme, :string
    attribute :padding_y, :string
    attribute :padding_x, :string
    attribute :container, :string
    attribute :gap_y, :string
    attribute :alignment, :string
    attribute :media_alignment, :string, default: "order-first"

    def initialize(attributes = {})
      super(attributes)
    end

    def theme_class
      theme == "auto" ? nil : theme
    end
  end
end