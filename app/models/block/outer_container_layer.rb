# app/models/block/outer_container_layer.rb (nebo kam jej um<PERSON>u<PERSON>)
class Block
  class OuterContainerLayer
    include ActiveModel::Model
    include ActiveModel::Attributes # Pro typované atributy

    attribute :theme, :string
    attribute :background_overlay_opacity, :string
    attribute :padding_y, :string
    attribute :padding_x, :string
    attribute :container, :string
    attribute :gap_y, :string

    def initialize(attributes = {})
      super(attributes)
    end

    def theme_class
      theme == "auto" ? nil : theme
    end
  end
end