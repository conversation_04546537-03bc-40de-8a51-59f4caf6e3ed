# == Schema Information
#
# Table name: media_types
#
#  id         :bigint           not null, primary key
#  has_groups :boolean          default(FALSE)
#  name       :string
#  slug       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#
class MediaType < ApplicationRecord
  has_many :media_fields, dependent: :destroy
  has_many :media, class_name: 'Media', dependent: :restrict_with_error

  validates :name, presence: true, uniqueness: true

  def fields_by_type(field_type)
    media_fields.where(field_type: field_type)
  end

  def required_fields
    media_fields.where(required: true)
  end

  def has_groups?
    has_groups
  end
end
