# == Schema Information
#
# Table name: html_tags
#
#  id         :bigint           not null, primary key
#  active     :boolean
#  content    :text
#  name       :string
#  position   :integer
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  page_id    :bigint           not null
#
# Foreign Keys
#
#  fk_rails_...  (page_id => pages.id)
#

class HtmlTag < ApplicationRecord
  belongs_to :page

  enum :position, head: 0, body: 1

  def self.position_name(key)
    return "<PERSON><PERSON><PERSON><PERSON><PERSON> <head>" if key == "head"
    "Tělo <body>" if key == "body"
  end
end
