# == Schema Information
#
# Table name: services
#
#  id           :bigint           not null, primary key
#  options      :jsonb
#  processed_at :datetime
#  type         :string
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  website_id   :bigint           not null
#
# Indexes
#
#  index_services_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#

class Service < ApplicationRecord
  acts_as_tenant(:website)

  SERVICES = [
    { name: "Google Maps", slug: "google-maps", class_name: "Services::GoogleMaps", label: "<PERSON><PERSON><PERSON><PERSON>, otevírací doba a další informace z Google Maps" },
    { name: "Instagram", slug: "instagram", class_name: "Services::Instagram", label: "Přístpěvky z instagramu na váš web" }
  ]

  def self.fetch_by_slug(slug)
    SERVICES.find { |service| service[:slug] == slug }
  end
end
