# == Schema Information
#
# Table name: forms
#
#  id         :bigint           not null, primary key
#  enabled    :boolean          default(TRUE)
#  form_type  :integer
#  name       :string
#  options    :jsonb
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  website_id :bigint           not null
#
# Indexes
#
#  index_forms_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#
class Form < ApplicationRecord
  acts_as_tenant(:website)
  store_accessor :options, :minimum_hours_before_reservation, :sms_on_reservation_confirm, :sms_on_reservation_cancel, :opening_hours_text

  enum :form_type, { contact: 0, reservation: 1 }
end
