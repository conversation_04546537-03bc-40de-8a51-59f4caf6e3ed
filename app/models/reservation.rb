# == Schema Information
#
# Table name: reservations
#
#  id         :bigint           not null, primary key
#  date       :date
#  email      :string
#  guests     :integer
#  name       :string
#  note       :text
#  phone      :string
#  status     :integer          default("pending")
#  time       :datetime
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  website_id :bigint           not null
#
# Indexes
#
#  index_reservations_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#
class Reservation < ApplicationRecord
  acts_as_tenant(:website)

  enum :status, [ :pending, :confirmed, :cancelled ]

  scope :open, -> { where(status: "pending").where("date >= ?", Date.current) }

  validates :name, :phone, :date, :time, presence: true

  def confirm!
    update(status: :confirmed)
  end

  def cancel!
    update(status: :cancelled)
  end

  def start_time
    DateTime.new(date.year, date.month, date.day, time.hour, time.min)
  end

  def duration
    items_by_id = {
      3 => 30,
      4 => 30,
      5 => 120,
      6 => 180,
      7 => 60,
      8 => 60,
      9 => 30
    }

    items_by_id[id]
  end

  def self.available_time_slots_by_range(start_date, end_date, slot_duration)
    # Fetch all reservations for the entire month in one query and group by date
    reservations_by_date = where(date: start_date..end_date).order(:date, :time).group_by(&:date)

    # Iterate over each day of the month and check for available slots
    (start_date..end_date).map do |date|
      opening = OpeningHour.opening_by_day(date)

      next { day: date, available: false } if opening.nil?

      opening_hour = Time.zone.local(date.year, date.month, date.day, opening.opening_hour.hour, opening.opening_hour.min, opening.opening_hour.sec)
      closing_hour = Time.zone.local(date.year, date.month, date.day, opening.closing_hour.hour, opening.closing_hour.min, opening.closing_hour.sec)

      available = slot_duration.zero? ? false : has_available_slots?(reservations_by_date[date] || [], opening_hour, closing_hour, slot_duration)
      { day: date, available: available }
    end
  end

  def self.available_time_slots(date, opening_time, closing_time, slot_duration)
    # Fetch all reservations for the given date
    reservations = where(date: date).order(:time)
    opening_hour = Time.zone.local(date.year, date.month, date.day, opening_time.hour, opening_time.min, opening_time.sec)
    closing_hour = Time.zone.local(date.year, date.month, date.day, closing_time.hour, closing_time.min, closing_time.sec)

    calculate_available_slots(reservations, opening_hour, closing_hour, slot_duration)
  end

  private

  def self.has_available_slots?(reservations, opening_time, closing_time, slot_duration)
    calculate_available_slots(reservations, opening_time, closing_time, slot_duration).any?
  end

  def self.calculate_available_slots(reservations, opening_time, closing_time, slot_duration)
    available_slots = []
    current_time = opening_time

    return [] if opening_time.nil? || closing_time.nil? || slot_duration.zero?

    reservations.each do |reservation|
      reserved_start_time = reservation.time
      reserved_end_time = reservation.time + reservation.duration.minutes

      if reserved_start_time > current_time
        while current_time + slot_duration.minutes <= reserved_start_time
          available_slots << current_time
          current_time += slot_duration.minutes
        end
      end

      current_time = [ current_time, reserved_end_time ].max
    end

    while current_time + slot_duration.minutes <= closing_time
      available_slots << current_time
      current_time += slot_duration.minutes
    end

    available_slots
  end
end
