# == Schema Information
#
# Table name: opening_hours
#
#  id           :bigint           not null, primary key
#  closing_hour :time
#  date         :datetime
#  day          :integer          default(0)
#  opening_hour :time
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  website_id   :bigint           not null
#
# Indexes
#
#  index_opening_hours_on_website_id           (website_id)
#  index_opening_hours_on_website_id_and_date  (website_id,date) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#
class OpeningHour < ApplicationRecord
  acts_as_tenant(:website)

  validate :opening_and_closing_hours_presence
  validates_uniqueness_of :date, scope: :website_id, allow_nil: true

  def self.holiday_dates
    {
      '2024' => %w[2024-09-28 2024-10-28 2024-11-17 2024-12-24 2024-12-25 2024-12-26 2024-12-31],
      '2025' => %w[2025-09-28 2025-10-28 2025-11-17 2025-12-24 2025-12-25 2025-12-26 2025-12-31]
    }
  end

  def custom_date?
    date.present? && OpeningHour.holiday_dates.values.flatten.exclude?(date.to_date.to_s)
  end

  def opening_hour
    time_with_current_date(self[:opening_hour])
  end

  # Custom getter for closing_hour
  def closing_hour
    time_with_current_date(self[:closing_hour])
  end

  def self.for_week
    days_of_week = (0..6).to_a # Sunday (0) to Saturday (6)
    today = Date.today
    specific_dates = where(date: today.beginning_of_week..today.end_of_week)
    generic_days = where(day: days_of_week, date: nil)

    { specific_dates: specific_dates, generic_days: generic_days }
  end

  def self.by_days
    days_of_week = (0..6).to_a
    days = where(day: days_of_week, date: nil)

    days.map { |day| [ day.day, { opening_hour: day.opening_hour.strftime("%H:%M"), closing_hour: day.closing_hour.strftime("%H:%M") } ] }.to_h
  end

  def self.by_month(month)
    days = where(date: month.beginning_of_month..month.end_of_month)

    days.map { |day| [ day.date, { opening_hour: day.opening_hour.strftime("%H:%M"), closing_hour: day.closing_hour.strftime("%H:%M") } ] }.to_h
  end

  def self.opening_by_day(day)
    return nil if day.nil?

    opening_hour = OpeningHour.find_by(date: day) || OpeningHour.find_by(day: day.wday)

    opening_hour || nil
  end

  private

  def time_with_current_date(time)
    return if time.nil?
    current_date = Date.current
    Time.zone.local(current_date.year, current_date.month, current_date.day, time.hour, time.min, time.sec)
  end

  def opening_and_closing_hours_presence
    if opening_hour.blank? && closing_hour.present?
      errors.add(:opening_hour, "must be filled if closing hour is filled")
    elsif closing_hour.blank? && opening_hour.present?
      errors.add(:closing_hour, "must be filled if opening hour is filled")
    end
  end
end
