# == Schema Information
#
# Table name: pricing
#
#  id           :bigint           not null, primary key
#  locale       :string
#  name         :string
#  position     :integer
#  pricing_type :string
#  valid_from   :datetime
#  valid_to     :datetime
#  created_at   :datetime         not null
#  updated_at   :datetime         not null
#  website_id   :bigint           not null
#
# Indexes
#
#  index_pricing_on_website_id  (website_id)
#
# Foreign Keys
#
#  fk_rails_...  (website_id => websites.id)
#
class Pricing < ApplicationRecord
  self.table_name = "pricing"

  acts_as_tenant(:website)
  positioned on: :website

  has_many :pricing_sections, -> { order(position: :asc) }, dependent: :destroy
  accepts_nested_attributes_for :pricing_sections, reject_if: :all_blank, allow_destroy: true

  default_scope { order(position: :asc) }

  scope :active, -> { where("(valid_from IS NULL OR valid_from <= ?) AND (valid_to IS NULL OR valid_to >= ?)", Time.zone.now, Time.zone.now) }
  scope :active_or_future, -> { where("valid_to IS NULL OR valid_to >= ?", Time.zone.now) }
  scope :expired, -> { where("valid_to < ?", Time.zone.now) }

  before_create :create_daily_menu_sections, if: :daily?

  validates :name, presence: true
  validates :valid_from, :valid_to, presence: true, if: -> { daily? || seasonal? }

  def regular?
    pricing_type == "regular" || pricing_type.nil?
  end

  def daily?
    pricing_type == "daily"
  end

  def seasonal?
    pricing_type == "seasonal"
  end

  def to_combobox_display
    name
  end

  def create_daily_menu_sections
    (valid_from.to_date..valid_to.to_date).to_a.each_with_index do |date, index|
      pricing_sections.build(name: date.strftime("%d.%m."), position: index + 1, valid_date: date)
    end
  end
end
