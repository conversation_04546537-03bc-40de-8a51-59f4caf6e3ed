# == Schema Information
#
# Table name: media_fields
#
#  id            :bigint           not null, primary key
#  field_key     :string
#  field_type    :string
#  position      :integer
#  required      :boolean
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  media_type_id :bigint           not null
#
# Indexes
#
#  index_media_fields_on_media_type_id  (media_type_id)
#
# Foreign Keys
#
#  fk_rails_...  (media_type_id => media_types.id)
#
class MediaField < ApplicationRecord
  belongs_to :media_type

  FIELD_TYPES = %w[string text content icon image rating file].freeze

  validates :field_key, presence: true
  validates :field_type, presence: true, inclusion: { in: FIELD_TYPES }
  validates :position, presence: true

  default_scope { order(:position) }

  def human_name
    field_key.humanize
  end
end
