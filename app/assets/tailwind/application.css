@import "tailwindcss";
@import 'tippy.js/dist/tippy.css';
@plugin "daisyui";

@plugin "daisyui/theme" {
    name: "white";
    prefersdark: false; /* set as default dark mode (prefers-color-scheme:dark) */
    color-scheme: light; /* color of browser-provided UI */

    --color-base-100: #fff;
    --color-base-content: #000;
    --color-accent: var(--color-soft-accent);
    --color-accent-content: var(--color-soft-accent-content);
    --radius-selector: var(--custom-radius-selector);  --radius-field: var(--custom-radius-field);  --radius-box: var(--custom-radius-box);  --size-selector: var(--custom-size-selector);  --size-field: var(--custom-size-field);  --border: var(--custom-options-border);  --depth: var(--custom-options-depth);  --noise: var(--custom-options-noise);
}

@plugin "daisyui/theme" {
    name: "black";
    prefersdark: false; /* set as default dark mode (prefers-color-scheme:dark) */
    color-scheme: dark; /* color of browser-provided UI */

    --color-base-100: #000;
    --color-base-content: #fff;
    --color-accent: var(--color-neutral-accent);
    --color-accent-content: var(--color-neutral-accent-content);
    --radius-selector: var(--custom-radius-selector);  --radius-field: var(--custom-radius-field);  --radius-box: var(--custom-radius-box);  --size-selector: var(--custom-size-selector);  --size-field: var(--custom-size-field);  --border: var(--custom-options-border);  --depth: var(--custom-options-depth);  --noise: var(--custom-options-noise);
}

@plugin "daisyui/theme" {
    name: "soft";
    prefersdark: false; /* set as default dark mode (prefers-color-scheme:dark) */
    color-scheme: light; /* color of browser-provided UI */

    --color-base-100: var(--color-soft-base);
    --color-base-content: var(--color-soft-base-content);
    --color-accent: var(--color-soft-accent);
    --color-accent-content: var(--color-soft-accent-content);
    --radius-selector: var(--custom-radius-selector);  --radius-field: var(--custom-radius-field);  --radius-box: var(--custom-radius-box);  --size-selector: var(--custom-size-selector);  --size-field: var(--custom-size-field);  --border: var(--custom-options-border);  --depth: var(--custom-options-depth);  --noise: var(--custom-options-noise);
}

@plugin "daisyui/theme" {
    name: "light";
    prefersdark: false; /* set as default dark mode (prefers-color-scheme:dark) */
    color-scheme: light; /* color of browser-provided UI */

    --color-base-100: var(--color-light-base);
    --color-base-content: var(--color-light-base-content);
    --color-accent: var(--color-light-accent);
    --color-accent-content: var(--color-light-accent-content);
    --radius-selector: var(--custom-radius-selector);  --radius-field: var(--custom-radius-field);  --radius-box: var(--custom-radius-box);  --size-selector: var(--custom-size-selector);  --size-field: var(--custom-size-field);  --border: var(--custom-options-border);  --depth: var(--custom-options-depth);  --noise: var(--custom-options-noise);
}

@plugin "daisyui/theme" {
    name: "neutral";
    prefersdark: false; /* set as default dark mode (prefers-color-scheme:dark) */
    color-scheme: dark; /* color of browser-provided UI */

    --color-base-100: var(--color-neutral-base);
    --color-base-content: var(--color-neutral-content);
    --color-accent: var(--color-neutral-accent);
    --color-accent-content: var(--color-neutral-accent-content);
    --radius-selector: var(--custom-radius-selector);  --radius-field: var(--custom-radius-field);  --radius-box: var(--custom-radius-box);  --size-selector: var(--custom-size-selector);  --size-field: var(--custom-size-field);  --border: var(--custom-options-border);  --depth: var(--custom-options-depth);  --noise: var(--custom-options-noise);
}

@plugin "daisyui/theme" {
    name: "primary";
    prefersdark: false; /* set as default dark mode (prefers-color-scheme:dark) */
    color-scheme: light; /* color of browser-provided UI */

    --color-base-100: var(--color-primary-base);
    --color-base-content: var(--color-primary-base-content);
    --color-accent: var(--color-primary-accent);
    --color-accent-content: var(--color-primary-accent-content);
    --radius-selector: var(--custom-radius-selector);  --radius-field: var(--custom-radius-field);  --radius-box: var(--custom-radius-box);  --size-selector: var(--custom-size-selector);  --size-field: var(--custom-size-field);  --border: var(--custom-options-border);  --depth: var(--custom-options-depth);  --noise: var(--custom-options-noise);
}


@plugin "daisyui/theme" {
    name: "admin";
    default: false;
    prefersdark: false;
    color-scheme: "light";
    --color-base-100: #fff;
    --color-base-200: #f5f4f0;
    --color-base-300: #e9e9de;
    --color-base-content: oklch(27.807% 0.029 256.847);
    --color-primary: #666947;
    --color-primary-content: #fff;
    --color-secondary: oklch(53.92% 0.162 241.36);
    --color-secondary-content: oklch(90.784% 0.032 241.36);
    --color-accent: oklch(0.512 0.099 227.758);
    --color-accent-content: oklch(1 0 89.876);
    --color-neutral: oklch(27% 0.041 260.031);
    --color-neutral-content: oklch(1 0 89.876);
    --color-info: oklch(72.06% 0.191 231.6);
    --color-info-content: oklch(0% 0 0);
    --color-success: oklch(64.8% 0.15 160);
    --color-success-content: oklch(0% 0 0);
    --color-warning: oklch(0.745 0.152 83.458);
    --color-warning-content: oklch(1 0 89.876);
    --color-error: oklch(71.76% 0.221 22.18);
    --color-error-content: oklch(1 0 89.876);
    --radius-selector: 0.25rem;
    --radius-field: 0.25rem;
    --radius-box: 0.25rem;
    --size-selector: 0.25rem;
    --size-field: 0.25rem;
    --border: 1px;
    --depth: 0;
    --noise: 0;
}


@theme {
    --breakpoint-3xl: 120rem;
    --color-avocado-50: #f5f4f0;
    --color-avocado-100: #e9e9de;
    --color-avocado-200: #d5d5c1;
    --color-avocado-300: #babb9b;
    --color-avocado-400: #95976b;
    --color-avocado-500: #82855d;
    --color-avocado-600: #666947;
    --color-avocado-700: #4f5239;
    --color-avocado-800: #414331;
    --color-avocado-900: #393b2c;
    --color-avocado-950: #1d1e15;

    --radius-field: 2rem;
}

body {
    @apply font-sans text-base text-black
}

.label {
    @apply block text-black font-medium mb-1 text-sm
}

.heading {
    @apply text-center text-black text-4xl sm:text-5xl
}

.block-content h1, .block-content h2, .block-content h3, .block-content h4 {
    padding: .6em 0 3px;
    margin: 0;
    outline: 0;
}

.control-container p:not(:first-of-type) {
    padding: .7em 0 0 0;
}

.control-container .h1 {
    @apply text-5xl sm:text-6xl
}

.control-container .h2 {
    @apply text-4xl sm:text-5xl
}

.control-container .h3 {
    @apply text-3xl sm:text-4xl
}

.control-container .h4 {
    @apply text-2xl sm:text-3xl
}

.control-container .h5 {
    @apply text-xl sm:text-2xl
}

.control-container .h5 {
    @apply text-lg sm:text-xl
}


.control-container h4 {
    @apply text-lg
}

.mobile-padding {
    @apply px-8 sm:px-0
}

.container-full {
    @apply max-w-screen mx-auto
}

.container-sm {
    @apply max-w-screen-sm mx-auto
}

.container-md {
    @apply max-w-screen-md mx-auto
}

.container-lg {
    @apply max-w-screen-lg mx-auto
}

.container-xl {
    @apply max-w-screen-xl mx-auto
}

.container-2xl {
    @apply max-w-screen-2xl mx-auto
}

.control-container ul,
.tiptap-content-editor ul {
    @apply list-disc list-inside py-4 pl-8;
}

.control-container ul li,
.tiptap-content-editor ul li {
    padding: 5.5px 0 5.5px 3px;
}

.control-container ol,
.tiptap-content-editor ol {
    @apply list-decimal list-inside py-4 pl-8;
}

.control-container ol li,
.tiptap-content-editor ol li {
    padding: 5.5px 0 5.5px 3px;
}


.control-container img {
    @apply my-3
}

.hw-combobox__main__wrapper {
    width: 100% !important;
}

.hw-combobox {
    width: 100% !important;
}

.control-container mark {
    @apply py-0.5 px-1 rounded-sm
}

.alignment-left {
    @apply items-start;

    & span, & h1, & h2, & h3, & .control-container {
        @apply text-left
    }
}

.alignment-center {
    @apply items-center;

    & span, & h1, & h2, & h3, & .control-container {
        @apply text-center
    }
}

.alignment-right {
    @apply items-end;

    & span, & h1, & h2, & h3, & .control-container {
        @apply text-right
    }
}

li.dish > div {
    display: flex;
    flex: 1;
}

li.dish div.item::after {
    content: " ";
    flex: 1;
    border-bottom-style: dotted;
    border-bottom-width: 1px;
    margin-bottom: 7px;
    margin-left: 7px;
    margin-right: 7px;
}

/* Hero007 animace a styly */
@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.hero007-gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, var(--color-accent), var(--color-primary));
  animation: pulse 3s infinite;
}

.hero007-shadow {
  box-shadow: 0 10px 50px -5px rgba(var(--color-accent-rgb), 0.3);
}

.hero007-glow {
  position: relative;
}

.hero007-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  padding: 2px;
  background: linear-gradient(to right, var(--color-accent), var(--color-primary));
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.hero007-glow:hover::before {
  opacity: 1;
}

.bg {
    width: 100%;
    height: 100vh;
    object-fit: cover;
    display: block;
    aspect-ratio: 16 / 9;
}

.text-instagram {
    @apply bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent
}

.tippy-box {
    background: transparent !important;
}

.tippy-arrow {
    display: none !important;
}