<%= form_with model: [:admin, :settings, @contact_form] do |f| %>
  <%= f.hidden_field :form_type %>
  <div class="relative bg-white flex">
    <div class="flex-1 focus:outline-none">
      <div class="relative max-w-4xl mx-auto md:px-8 xl:px-0">
        <div class="py-10">
          <div class="px-4 sm:px-6 md:px-0 flex space-x-2 items-center">
            <h1 class="text-2xl font-semibold text-gray-900">
              Kontaktní formulář
            </h1>
          </div>

          <div class="mt-5">
            <div class="bg-white flex flex-col space-y-4">
              <label class="flex items-center space-x-2">
                <button type="button"
                        class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2"
                        role="switch"
                        aria-checked="false"
                        data-controller="toggle-checkbox"
                        data-action="click->toggle-checkbox#toggle"
                        data-turbo-permanent
                        data-toggle-checkbox-target="button">
                  <%= f.check_box :enabled, class: "hidden", data: { toggle_checkbox_target: "checkbox" } %>
                  <span class="pointer-events-none relative inline-block h-5 w-5 translate-x-0 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                        data-toggle-checkbox-target="toggleSpan">
                        <span class="absolute inset-0 flex h-full w-full items-center justify-center opacity-100 transition-opacity duration-200 ease-in"
                              aria-hidden="true"
                              data-toggle-checkbox-target="iconOff">
                          <svg class="h-3 w-3 text-gray-400" fill="none" viewBox="0 0 12 12">
                            <path d="M4 8l2-2m0 0l2-2M6 6L4 4m2 2l2 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                          </svg>
                        </span>
                        <span class="absolute inset-0 flex h-full w-full items-center justify-center opacity-0 transition-opacity duration-100 ease-out"
                              aria-hidden="true"
                              data-toggle-checkbox-target="iconOn">
                          <svg class="h-3 w-3 text-indigo-600" fill="currentColor" viewBox="0 0 12 12">
                            <path d="M3.707 5.293a1 1 0 00-1.414 1.414l1.414-1.414zM5 8l-.707.707a1 1 0 001.414 0L5 8zm4.707-3.293a1 1 0 00-1.414-1.414l1.414 1.414zm-7.414 2l2 2 1.414-1.414-2-2-1.414 1.414zm3.414 2l4-4-1.414-1.414-4 4 1.414 1.414z" />
                          </svg>
                        </span>
                      </span>
                </button>
                <span class="text-sm">Povolit kontaktní formulář</span>
              </label>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
  <div class="py-3 bg-base-200/80 border-t-2 border-primary/50">
    <div class="max-w-4xl mx-auto">
      <button type="submit" class="button">Uložit nastavení</button>
    </div>
  </div>
<% end %>

<%= form_with model: [:admin, :settings, @reservation_form] do |f| %>
  <%= f.hidden_field :form_type %>
  <div class="relative bg-white flex">
    <div class="flex-1 focus:outline-none">
      <div class="relative max-w-4xl mx-auto md:px-8 xl:px-0">
        <div class="py-10">
          <div class="px-4 sm:px-6 md:px-0 flex space-x-2 items-center">
            <h1 class="text-2xl font-semibold text-gray-900">
              Rezervační formulář
            </h1>
          </div>

          <div class="mt-5">
            <div class="bg-white flex flex-col space-y-4">
              <label class="flex items-center space-x-2">
                <button type="button"
                        class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2"
                        role="switch"
                        aria-checked="false"
                        data-controller="toggle-checkbox"
                        data-action="click->toggle-checkbox#toggle"
                        data-turbo-permanent
                        data-toggle-checkbox-target="button">
                  <%= f.check_box :enabled, class: "hidden", data: { toggle_checkbox_target: "checkbox" } %>
                  <span class="pointer-events-none relative inline-block h-5 w-5 translate-x-0 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                        data-toggle-checkbox-target="toggleSpan">
                        <span class="absolute inset-0 flex h-full w-full items-center justify-center opacity-100 transition-opacity duration-200 ease-in"
                              aria-hidden="true"
                              data-toggle-checkbox-target="iconOff">
                          <svg class="h-3 w-3 text-gray-400" fill="none" viewBox="0 0 12 12">
                            <path d="M4 8l2-2m0 0l2-2M6 6L4 4m2 2l2 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                          </svg>
                        </span>
                        <span class="absolute inset-0 flex h-full w-full items-center justify-center opacity-0 transition-opacity duration-100 ease-out"
                              aria-hidden="true"
                              data-toggle-checkbox-target="iconOn">
                          <svg class="h-3 w-3 text-indigo-600" fill="currentColor" viewBox="0 0 12 12">
                            <path d="M3.707 5.293a1 1 0 00-1.414 1.414l1.414-1.414zM5 8l-.707.707a1 1 0 001.414 0L5 8zm4.707-3.293a1 1 0 00-1.414-1.414l1.414 1.414zm-7.414 2l2 2 1.414-1.414-2-2-1.414 1.414zm3.414 2l4-4-1.414-1.414-4 4 1.414 1.414z" />
                          </svg>
                        </span>
                      </span>
                </button>
                <span class="text-sm">Povolit rezervace</span>
              </label>
            </div>
          </div>

          <div class="mt-5 max-w-xs">
            <div class="bg-white flex flex-col space-y-4">
              <div>
                <label>Vytvoření rezervace před termínem nejpozději</label>
                <div>
                  <div>
                    <div class="relative mt-2 rounded-md shadow-sm">
                      <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                        <span class="text-gray-500 sm:text-sm">+</span>
                      </div>
                      <%= f.text_field :minimum_hours_before_reservation, class: "form-input pl-7" %>
                      <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <span class="text-gray-500 sm:text-sm" id="price-currency">Hodin</span>
                      </div>
                    </div>
                    <% if current_tenant.errors.full_messages_for(:minimum_hours_before_reservation).any? %>
                      <div class="flex space-x-2 bg-red-50 text-red-600 px-3 py-2 text-sm items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5 text-red-600">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                        </svg>
                        <div>
                            Zadejte platnou hodnotu (číslo)
                          </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-5 max-w-xs">
            <div class="bg-white flex flex-col space-y-4">
              <div>
                <label>Maximální počet dnů pro rezervaci předem</label>
                <div>
                  <div>
                    <div class="relative mt-2 rounded-md shadow-sm">
                      <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                        <span class="text-gray-500 sm:text-sm">+</span>
                      </div>
                      <%= f.text_field :minimum_hours_before_reservation, class: "form-input pl-7" %>
                      <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                        <span class="text-gray-500 sm:text-sm" id="price-currency">Dnů</span>
                      </div>
                    </div>
                    <% if current_tenant.errors.full_messages_for(:minimum_hours_before_reservation).any? %>
                      <div class="flex space-x-2 bg-red-50 text-red-600 px-3 py-2 text-sm items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5 text-red-600">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                        </svg>
                        <div>
                          Zadejte platnou hodnotu (číslo)
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-4 relative flex items-start checkbox">
            <div class="flex h-6 items-center">
              <%= f.check_box :sms_on_reservation_confirm, class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" %>            </div>
            <div class="ml-3 text-sm leading-6">
              <label for="sms_confirm" class="label">SMS při potvrzení rezervace</label>
              <p id="comments-description" class="text-gray-500">
                Posílat zákazníkům SMS zprávy v případě potvrzení rezervace
              </p>
            </div>
          </div>


          <div class="mt-4 relative flex items-start checkbox">
            <div class="flex h-6 items-center">
              <%= f.check_box :sms_on_reservation_cancel, class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" %>
            </div>
            <div class="ml-3 text-sm leading-6">
              <label for="sms_reject" class="label">SMS při zrušení rezervace</label>
              <p id="comments-description" class="text-gray-500">
                Posílat zákazníkům SMS zprávy v případě zrušení rezervace
              </p>
            </div>
          </div>

          <label class="label mt-4 relative flex items-start checkbox">
            <div class="flex h-6 items-center">
              <%= f.check_box :sms_on_reservation_confirm, class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" %>            </div>
            <div class="ml-3 text-sm leading-6">
               SMS připomenutí
              <p id="comments-description" class="text-gray-500">
                Připomenutí rezervace 24 hodin předem
              </p>
            </div>
          </label>

          <label class="mt-4 relative flex items-start checkbox">
            <div class="flex h-6 items-center">
              <%= f.check_box :sms_on_reservation_confirm, class: "h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-600" %>
            </div>
            <div class="ml-3 text-sm leading-6">
             Propojit rezervace s kalendářem
            </div>
          </label>
        </div>
      </div>
    </div>
  </div>
  <div class="py-3 bg-base-200/80 border-t-2 border-primary/50">
    <div class="max-w-4xl mx-auto">
      <button type="submit" class="button">Uložit nastavení</button>
    </div>
  </div>
<% end %>
