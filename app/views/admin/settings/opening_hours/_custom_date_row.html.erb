<tr class="parent-class nested-form-wrapper" data-new-record="<%= o.object.new_record? %>" data-controller="opening-hours" data-action="turbo:morph-element->opening-hours#reconnect">
  <td class="py-2 pl-3 pr-12 w-full">
    <div>
      <%= o.hidden_field :id %>
      <%= o.hidden_field :date %>
      <%= o.hidden_field :day, value: nil %>
      <div class="flex items-center">
        <%= o.check_box :_destroy, { checked: o.object.persisted? || (o.object.new_record? && o.object.custom_date?) || date.nil?, class: "checkbox bg-white checked:bg-white", data: {'opening-hours-target': "checkbox"} }, "0", "1" %>
        <span class="pl-2">
          <% if date %>
            <%= l date %>
          <% else %>
            <%= o.date_field :date, class: "input" %>
          <% end %>
         </span>
      </div>
    </div>
  </td>
  <td class="py-2">
    <%= o.time_field :opening_hour, class: "input", data: {'opening-hours-target': "timeInput"} %>
  </td>
  <td class="px-4">—</td>
  <td class="pr-3">
    <%= o.time_field :closing_hour, class: "input", data: {'opening-hours-target': "timeInput"} %>
  </td>
</tr>