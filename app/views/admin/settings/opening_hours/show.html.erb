<%= form_with model: @website, url: admin_settings_opening_hours_path do |form| %>
  <% if @website.errors.messages_for(:opening_hours).any? %>
    <div class="rounded-md bg-orange-50 p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-orange-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3 flex-1 md:flex md:justify-between">
          <p class="text-sm text-orange-900">
            <PERSON><PERSON><PERSON><PERSON> <strong>OD—DO</strong> u všech aktivních dnů otevírací doby
          </p>
        </div>
      </div>
    </div>
  <% end %>

  <div class="relative bg-white flex">
    <div class="flex-1 focus:outline-none">
      <div class="relative  mx-auto md:px-8 xl:px-12">
        <div class="py-10">
          <div class="flex divide-x divide-gray-200">
            <div class="pr-4">
              <div class="flex items-center">
                <h1 class="text-xl font-semibold text-gray-900">
                  Základní otevírací doba
                </h1>
              </div>
              <table class="w-full mt-5">
                <thead class="text-left">
                  <tr>
                    <th class="font-medium text-sm pb-3">Den</th>
                    <th class="font-medium text-sm">Od</th>
                    <th></th>
                    <th class="font-medium text-sm">Do</th>
                  </tr>
                </thead>
                <tbody>
                <% weekdays.each_with_index do |day, index| %>
                  <%= form.fields_for :opening_hours, @website.opening_hours.find_or_initialize_by(day: day[:value]) do |o| %>
                    <tr class="<%= index % 2 == 0 ? 'bg-avocado-50' : '' %> parent-class" data-controller="opening-hours" data-turbo-permanent>
                      <td class="py-2 pl-3 pr-12">
                        <div>
                          <%= o.hidden_field :day %>
                          <div class="flex items-center">
                            <%= o.check_box :_destroy, { checked: o.object.persisted?, class: "checkbox bg-white checked:bg-white", data: {'opening-hours-target': "checkbox"} }, "0", "1" %>
                            <span class="pl-2">
                                <%= day[:label] %>
                              </span>
                          </div>
                        </div>
                      </td>
                      <td class="py-2">
                        <%= o.time_field :opening_hour, class: "input", data: {'opening-hours-target': "timeInput"} %>
                      </td>
                      <td class="px-4">—</td>
                      <td class="pr-3">
                        <%= o.time_field :closing_hour, class: "input", data: {'opening-hours-target': "timeInput"} %>
                      </td>
                    </tr>
                  <% end %>
                <% end %>
                </tbody>
              </table>

              <div class="mt-3">
                <label class="label">Vlastní interpretace otevírací doby:</label>
                <%= form.text_field :opening_hours_text, class: "input" %>
              </div>
            </div>
            <div class="flex-1 pl-4" data-controller="nested-form" data-nested-form-wrapper-selector-value=".nested-form-wrapper">
              <h1 class="text-xl font-semibold text-gray-900">
                Speciální otevírací doba
              </h1>

              <template data-nested-form-target="template">
                <%= form.fields_for :opening_hours, @website.opening_hours.find_or_initialize_by(date: Date.today), child_index: "NEW_RECORD" do |o| %>
                  <%= render "custom_date_row", date: nil, o: %>
                <% end %>
              </template>

                <table class="w-full mt-5">
                  <thead class="text-left">
                    <tr>
                      <th class="font-medium text-sm pb-3">Datum</th>
                      <th class="font-medium text-sm">Od</th>
                      <th></th>
                      <th class="font-medium text-sm">Do</th>
                    </tr>
                  </thead>
                  <tbody class="opening-hours-tbody">
                    <% OpeningHour::holiday_dates[Time.now.year.to_s].each_with_index do |date, index| %>
                      <%= form.fields_for :opening_hours, @website.opening_hours.find_or_initialize_by(date: date), include_id: false do |o| %>
                        <%= render "custom_date_row", date: Date.parse(date), o: %>
                      <% end %>
                    <% end %>

                    <% @custom_opening_hours.each_with_index do |opening_hour, index| %>
                      <%= form.fields_for :opening_hours, opening_hour, include_id: false do |o| %>
                        <%= render "custom_date_row", date: opening_hour.date.to_date, o: %>
                      <% end %>
                    <% end %>

                    <tr data-nested-form-target="target"></tr>
                  </tbody>
                </table>

              <div class="flex justify-end pt-3 mt-2 sm:pr-3 border-t">
                <button type="button" data-action="nested-form#add" class="flex items-center bg-avocado-50 hover:bg-avocado-100 text-avocado-900 px-2 py-1 text-sm">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                    <path d="M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 0 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z" />
                  </svg>

                  Přidat datum
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="py-3 bg-base-200/80 border-t-2 border-primary/50">
    <div class="px-12 mx-auto">
      <button type="submit" class="btn btn-primary">Uložit nastavení</button>
    </div>
  </div>
<% end %>
