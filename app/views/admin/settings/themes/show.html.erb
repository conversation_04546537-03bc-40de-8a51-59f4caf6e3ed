<!-- <PERSON><PERSON><PERSON><PERSON><PERSON> (mimo <PERSON>) -->
<div class="relative bg-white">
  <div class="flex-1 focus:outline-none">
    <div class="relative max-w-6xl mx-auto md:px-8 xl:px-0">
      <div class="py-6">
        <div class="px-4 sm:px-6 md:px-0">
          <h2 class="text-lg font-medium text-gray-900 mb-4">Šablony</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <% current_tenant.available_templates.each do |key, template| %>
              <div class="border border-gray-200 rounded-lg p-4 hover:border-gray-300 transition-colors <%= 'border-avocado-500 bg-avocado-50' if current_tenant.template_key == key.to_s %>">
                <h3 class="font-medium text-gray-900 mb-2"><%= template[:name] %></h3>
                <div class="flex space-x-2 mb-3">
                  <% template.dig(:colors)&.each do |theme_name, theme_data| %>
                    <% next unless theme_data.is_a?(Hash) && theme_data[:base] %>
                    <div class="w-4 h-4 rounded border border-gray-200"
                         style="background-color: <%= theme_data[:base] %>"
                         title="<%= theme_data[:name] || theme_name.to_s.humanize %>"></div>
                  <% end %>
                </div>
                <%= button_to "Načíst šablonu",
                    load_preset_admin_settings_theme_path,
                    method: :patch,
                    params: { template_key: key },
                    class: "btn btn-sm #{'btn-primary' if current_tenant.template_key == key.to_s} #{'btn-outline' unless current_tenant.template_key == key.to_s}",
                    data: { confirm: "Opravdu chcete načíst tuto šablonu? Aktuální nastavení bude přepsáno." } %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<%= form_with model: [:admin, current_tenant], url: admin_settings_theme_path, method: :patch do |f| %>
  <div class="relative bg-white flex">
    <div class="flex-1 focus:outline-none">
      <div class="relative max-w-6xl mx-auto md:px-8 xl:px-0">
        <div class="py-10">
          <div class="px-4 sm:px-6 md:px-0 flex space-x-2 items-center">
            <h1 class="text-2xl font-semibold text-gray-900">
              Úprava tématu
            </h1>
          </div>



          <!-- Úprava barevných témat -->
          <div class="mt-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Barevná témata</h2>
            <div class="space-y-8">
              <% current_tenant.available_color_themes.each do |theme_name, theme_data| %>
                <% next unless theme_data.is_a?(Hash) && theme_data.key?(:base) %>
                <div class="border border-gray-200 rounded-lg p-6">
                  <h3 class="text-md font-medium text-gray-900 mb-4">
                    <%= theme_data[:name] || theme_name.to_s.humanize %>
                    <span class="text-sm text-gray-500 font-normal">(<%= theme_name %>)</span>
                  </h3>

                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Základní barva</label>
                      <%= f.color_field "#{theme_name}_base",
                          value: current_tenant.send("#{theme_name}_base") || theme_data[:base],
                          class: "w-full h-10 border border-gray-300 rounded-md" %>
                      <p class="text-xs text-gray-500 mt-1">Barva pozadí</p>
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Barva textu</label>
                      <%= f.color_field "#{theme_name}_base_content",
                          value: current_tenant.send("#{theme_name}_base_content") || theme_data[:base_content],
                          class: "w-full h-10 border border-gray-300 rounded-md" %>
                      <p class="text-xs text-gray-500 mt-1">Barva textu na pozadí</p>
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Akcentní barva</label>
                      <%= f.color_field "#{theme_name}_accent",
                          value: current_tenant.send("#{theme_name}_accent") || theme_data[:accent],
                          class: "w-full h-10 border border-gray-300 rounded-md" %>
                      <p class="text-xs text-gray-500 mt-1">Barva pro zvýraznění</p>
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">Barva textu na akcentu</label>
                      <%= f.color_field "#{theme_name}_accent_content",
                          value: current_tenant.send("#{theme_name}_accent_content") || theme_data[:accent_content],
                          class: "w-full h-10 border border-gray-300 rounded-md" %>
                      <p class="text-xs text-gray-500 mt-1">Barva textu na akcentní barvě</p>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          </div>

          <!-- Obecná nastavení -->
          <div class="mt-8">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Obecná nastavení</h2>
            <div class="bg-white flex flex-col space-y-6">

              <!-- Typografie -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Písmo pro text</label>
                  <%= f.select :font_family,
                      options_for_select([
                        ['Inter (výchozí)', 'Inter, system-ui, sans-serif'],
                        ['Roboto', 'Roboto, sans-serif'],
                        ['Open Sans', 'Open Sans, sans-serif'],
                        ['Lato', 'Lato, sans-serif'],
                        ['Poppins', 'Poppins, sans-serif'],
                        ['Montserrat', 'Montserrat, sans-serif'],
                        ['Source Sans Pro', 'Source Sans Pro, sans-serif']
                      ], current_tenant.font_family || 'Inter, system-ui, sans-serif'),
                      { prompt: "Vyberte písmo" },
                      { class: "select w-full max-w-xs" } %>
                  <p class="text-xs text-gray-500 mt-1">Font pro běžný text a odstavce</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Písmo pro nadpisy</label>
                  <%= f.select :heading_font_family,
                      options_for_select([
                        ['Stejné jako text', ''],
                        ['--- Elegantní ---', '', { disabled: true }],
                        ['Playfair Display', 'Playfair Display, serif'],
                        ['Merriweather', 'Merriweather, serif'],
                        ['Crimson Text', 'Crimson Text, serif'],
                        ['--- Moderní ---', '', { disabled: true }],
                        ['Oswald', 'Oswald, sans-serif'],
                        ['Raleway', 'Raleway, sans-serif'],
                        ['Nunito', 'Nunito, sans-serif'],
                        ['--- Výrazné ---', '', { disabled: true }],
                        ['Bebas Neue', 'Bebas Neue, cursive'],
                        ['Anton', 'Anton, sans-serif'],
                        ['Fredoka One', 'Fredoka One, cursive'],
                        ['--- Kreativní ---', '', { disabled: true }],
                        ['Dancing Script', 'Dancing Script, cursive'],
                        ['Pacifico', 'Pacifico, cursive'],
                        ['Lobster', 'Lobster, cursive']
                      ], current_tenant.heading_font_family || ''),
                      { prompt: "Vyberte písmo pro nadpisy" },
                      { class: "select w-full max-w-xs" } %>
                  <p class="text-xs text-gray-500 mt-1">Speciální font pro h1, h2, h3 nadpisy</p>
                </div>
              </div>

              <!-- Styl prvků -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Zaoblení rohů</label>
                  <%= f.select :border_radius,
                      options_for_select([
                        ['Žádné (0px)', '0'],
                        ['Malé (4px)', '0.25rem'],
                        ['Střední (8px)', '0.5rem'],
                        ['Velké (12px)', '0.75rem'],
                        ['Extra velké (16px)', '1rem']
                      ], current_tenant.border_radius || current_tenant.current_template.dig(:radius, :field) || '0.5rem'),
                      { prompt: "Vyberte zaoblení" },
                      { class: "select w-full max-w-xs" } %>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Styl tlačítek</label>
                  <%= f.select :button_style,
                      options_for_select([
                        ['Plné', 'solid'],
                        ['Obrysové', 'outline'],
                        ['Průhledné', 'ghost']
                      ], current_tenant.button_style || 'solid'),
                      { prompt: "Vyberte styl" },
                      { class: "select w-full max-w-xs" } %>
                </div>
              </div>

              <!-- Logo nastavení -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Logo webu</label>
                  <div class="space-y-3">
                    <% if current_tenant.logo.attached? %>
                      <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border">
                        <div class="flex-shrink-0">
                          <% image = current_tenant.logo.variable? ? current_tenant.logo.variant(:resized) : current_tenant.logo %>
                          <%= image_tag image, class: "w-12 h-12 object-contain rounded border bg-white" %>
                        </div>
                        <div class="flex-1 min-w-0">
                          <p class="text-sm font-medium text-gray-900 truncate">Aktuální logo</p>
                          <p class="text-xs text-gray-500">Klikněte níže pro změnu</p>
                        </div>
                      </div>
                    <% else %>
                      <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border border-dashed">
                        <div class="flex-shrink-0">
                          <div class="w-12 h-12 bg-gray-200 rounded border flex items-center justify-center">
                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                          </div>
                        </div>
                        <div class="flex-1 min-w-0">
                          <p class="text-sm font-medium text-gray-900">Žádné logo</p>
                          <p class="text-xs text-gray-500">Nahrajte logo webu</p>
                        </div>
                      </div>
                    <% end %>
                    <%= f.file_field :logo, accept: "image/svg+xml,image/png,image/jpg,image/jpeg,image/gif", class: "file-input file-input-sm w-full" %>
                    <p class="text-xs text-gray-500">Podporované formáty: SVG, PNG, JPG, GIF</p>
                  </div>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Velikost loga</label>
                  <div class="space-y-3">
                    <%= f.select :logo_size, options_for_select(Website::LOGO_SIZES.map { |k, v| [k, k] }, current_tenant.logo_size),
                        { prompt: "Vyberte velikost (výchozí: 128px)" },
                        { class: "select w-full" } %>
                    <p class="text-xs text-gray-500">Maximální šířka loga v hlavičce webu</p>

                    <% if current_tenant.logo.attached? %>
                      <div class="p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <p class="text-xs font-medium text-blue-900 mb-1">Náhled velikosti:</p>
                        <div class="<%= current_tenant.logo_size_class %>">
                          <% image = current_tenant.logo.variable? ? current_tenant.logo.variant(:resized) : current_tenant.logo %>
                          <%= image_tag image, class: "w-auto h-auto object-contain max-h-16 border bg-white rounded" %>
                        </div>
                      </div>
                    <% end %>
                  </div>
                </div>
              </div>

              <!-- Skryté pole pro template_key -->
              <%= f.hidden_field :template_key, value: current_tenant.template_key || 'default' %>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="py-3 bg-avocado-100/70 border-t-2 border-avocado-200">
    <div class="max-w-6xl mx-auto">
      <button type="submit" class="btn btn-primary">Uložit nastavení</button>
    </div>
  </div>
<% end %>
