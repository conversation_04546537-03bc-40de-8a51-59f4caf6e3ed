<div class="sm:flex sm:items-center">
  <div class="sm:flex-auto">
    <h1 class="text-base font-semibold text-gray-900">Fotogalerie</h1>
  </div>
</div>

<table class="mt-4  min-w-full border-x border-b border-primary/40">
    <thead class="bg-base-200 border-t border-gray-300">
    <tr>
      <th scope="col" class="py-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3 flex items-center space-x-1.5">
        <span>Název</span>
      </th>
      <th scope="col" class="py-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">Obsah</th>
      <th scope="col" class="py-2 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"></th>
    </tr>
    </thead>

    <tbody data-controller="sortable" class="bg-white">
    <% @galleries.each do |gallery| %>
      <tr class="resource border-t border-gray-300 hover:bg-gray-50">
        <td class="align-center whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
          <%= gallery.name %>
        </td>
        <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
          <% if gallery.media.any? %>
            <div class="flex items-center -space-x-3">
              <% gallery.media.limit(5).each do |media| %>
                <%= image_tag media.image.variant(:thumb), class: "rounded-full border border-gray-300 p-px w-10 h-10" %>
              <% end %>

              <% if gallery.media.count > 5 %>
                <div class="rounded-full border border-gray-300 p-px w-10 h-10 bg-gray-100 text-gray-600 text-lg flex items-center justify-center">+<%= gallery.media.count - 5 %></div>
              <% end %>
            </div>
          <% else %>
            <div class="rounded-full border border-gray-300 p-px w-10 h-10 bg-gray-100 text-gray-600 text-lg flex items-center justify-center">0</div>
        <% end %>
        </td>
        <td class="whitespace-nowrap py-2 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
          <div class="flex justify-center items-center">
            <%= link_to admin_content_media_gallery_path(gallery),
                        data: { controller: "tippy", tippy_content: 'Detail fotogalerie' },
                        class: 'edit-link inline-block' do %>
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 0 1 1.45.12l.773.774c.39.389.44 1.002.12 1.45l-.527.737c-.25.35-.272.806-.107 1.204.165.397.505.71.93.78l.893.15c.543.09.94.559.94 1.109v1.094c0 .55-.397 1.02-.94 1.11l-.894.149c-.424.07-.764.383-.929.78-.165.398-.143.854.107 1.204l.527.738c.32.447.269 1.06-.12 1.45l-.774.773a1.125 1.125 0 0 1-1.449.12l-.738-.527c-.35-.25-.806-.272-1.203-.107-.398.165-.71.505-.781.929l-.149.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.148-.894c-.071-.424-.384-.764-.781-.93-.398-.164-.854-.142-1.204.108l-.738.527c-.447.32-1.06.269-1.45-.12l-.773-.774a1.125 1.125 0 0 1-.12-1.45l.527-.737c.25-.35.272-.806.108-1.204-.165-.397-.506-.71-.93-.78l-.894-.15c-.542-.09-.94-.56-.94-1.109v-1.094c0-.55.398-1.02.94-1.11l.894-.149c.424-.07.765-.383.93-.78.165-.398.143-.854-.108-1.204l-.526-.738a1.125 1.125 0 0 1 .12-1.45l.773-.773a1.125 1.125 0 0 1 1.45-.12l.737.527c.35.25.807.272 1.204.107.397-.165.71-.505.78-.929l.15-.894Z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
              </svg>
            <% end %>
          </div>
        </td>
      </tr>
    <% end %>
    </tbody>
</table>