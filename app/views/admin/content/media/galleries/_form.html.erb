<%= form_with model: [:admin, :content, :media, gallery] do |f| %>
  <div class="relative bg-white flex">
    <div class="flex-1 focus:outline-none">
      <div class="relative max-w-4xl mx-auto md:px-8 xl:px-0">
        <div class="py-10">
          <div class="px-4 sm:px-6 md:px-0 flex space-x-2 items-center">
            <a href="<%= admin_content_media_galleries_path %>" id="back-link" data-form-target="backLink">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
              </svg>
            </a>

            <h1 class="text-2xl font-semibold text-gray-900">
              <% if gallery.persisted? %>
                <%= gallery.name %>
              <% else %>
                Přidat fotogalerii
              <% end %>
            </h1>
          </div>

          <div class="mt-5">
            <div class="bg-white flex flex-col space-y-4">
              <div>
                <label>Název fotogalerie</label>
                <div>
                  <%= f.text_field :name, class: "input" %>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-5">
            <div class="bg-white flex flex-col space-y-4">
              <div>
                <label>Seznam fotografií</label>
                <div class="grid grid-cols-10 gap-3" data-controller="sortable">
                  <% gallery.media.each do |media| %>
                    <div class="group relative bg-gray-200" data-sortable-update-url="<%= sort_admin_content_media_gallery_path(media) %>">
                      <%= image_tag media.image.variant(:thumb), class: "rounded-box" %>
                      <div class="hidden group-hover:block absolute top-0 left-0 bg-black opacity-50 w-full h-full"></div>

                      <div class="hidden group-hover:flex bg-black/20 absolute top-0 left-0 w-full h-full justify-center items-center justify-center space-x-2">
                          <button class="handle btn p-0 px-1 btn-xs btn-accent">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                              <path fill-rule="evenodd" d="M8 3.5c-.771 0-1.537.022-2.297.066a1.124 1.124 0 0 0-1.058 1.028l-.018.214a.75.75 0 1 1-1.495-.12l.018-.221a2.624 2.624 0 0 1 2.467-2.399 41.628 41.628 0 0 1 4.766 0 2.624 2.624 0 0 1 2.467 2.399c.056.662.097 1.329.122 2l.748-.748a.75.75 0 1 1 1.06 1.06l-2 2.001a.75.75 0 0 1-1.061 0l-2-1.999a.75.75 0 0 1 1.061-1.06l.689.688a39.89 39.89 0 0 0-.114-1.815 1.124 1.124 0 0 0-1.058-1.028A40.138 40.138 0 0 0 8 3.5ZM3.22 7.22a.75.75 0 0 1 1.061 0l2 2a.75.75 0 1 1-1.06 1.06l-.69-.69c.025.61.062 1.214.114 1.816.048.56.496.996 1.058 1.028a40.112 40.112 0 0 0 4.594 0 1.124 1.124 0 0 0 1.058-1.028 39.2 39.2 0 0 0 .018-.219.75.75 0 1 1 1.495.12l-.018.226a2.624 2.624 0 0 1-2.467 2.399 41.648 41.648 0 0 1-4.766 0 2.624 2.624 0 0 1-2.467-2.399 41.395 41.395 0 0 1-.122-2l-.748.748A.75.75 0 1 1 1.22 9.22l2-2Z" clip-rule="evenodd" />
                            </svg>
                          </button>

                          <%= link_to admin_content_media_gallery_path(media, gallery_id: gallery), class: "btn btn-xs p-0 px-1 btn-error", data: { turbo_method: :delete } do %>
                              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                                <path d="M5.28 4.22a.75.75 0 0 0-1.06 1.06L6.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L8 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L9.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L8 6.94 5.28 4.22Z" />
                              </svg>
                          <% end %>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>
            </div>

            <div class="mt-5">
              <%= f.fields_for :media, @gallery.media.build do |m| %>
                <fieldset class="fieldset">
                  <legend class="fieldset-legend">Nahrát fotografie</legend>
                  <%= m.file_field :image, multiple: true, accept: "image/*", class: "file-input" %>
                </fieldset>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="py-3 bg-base-200/80 border-t-2 border-primary/50">
    <div class="max-w-4xl mx-auto">
      <button type="submit" class="btn btn-primary">Uložit nastavení</button>
    </div>
  </div>
<% end %>