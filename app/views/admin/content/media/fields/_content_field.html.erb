<%# Parciální šablona pro pole typu content %>
<div class="w-full h-auto">
  <div data-controller="tiptap" data-tiptap-preview-element-id-value="<%= "media-#{f.object.id}-text" %>" class="text-base" data-tiptap-text-value="<%= f.object.text %>">
    <%= f.hidden_field :text, placeholder: "Text nadpisu", data: { 'tiptap-target': 'inputText' } %>
    <%= f.hidden_field :content, class: "input", value: f.object.content.to_json, data: { 'tiptap-target': 'inputContent' } %>

    <div data-tiptap-target="content" data-theme="<%= block.theme %>" class="text-black bg-white rounded-none rounded-r h-auto rounded border border-gray-300 p-2"></div>

    <%= render "/admin/content/blocks/controls/tiptap_toolbar", block_theme: block.theme %>
  </div>
</div>
<% if medium.errors.messages_for(:content).any? %>
  <%= error_message "Toto pole je povinné" %>
<% end %>
