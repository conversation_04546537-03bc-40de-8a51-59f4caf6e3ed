<div class="bg-base-100 border border-base-300 px-4 py-4 rounded-box">
  <label class="label label-sm font-normal">Text</label>

  <div data-controller="tiptap" data-block-id="<%= b.object.id %>" data-tiptap-preview-element-id-value="<%= "control-#{b.object.id}" %>" class="text-base" data-tiptap-text-value="<%= b.object.text %>">
    <div data-tiptap-target="content" data-theme="<%= block.theme %>" class="textarea text-black tiptap-content-editor bg-white min-h-[100px] rounded border border-gray-300 p-2 outline-none ring-0"></div>

    <%= b.hidden_field :content, class: "text-field", value: b.object.content.to_json, data: { 'tiptap-target': 'inputContent' } %>
    <%= b.hidden_field :text, class: "text-field", value: b.object.text, data: { 'tiptap-target': 'inputText' } %>

    <%= render "admin/content/blocks/controls/tiptap_toolbar" %>
  </div>
</div>