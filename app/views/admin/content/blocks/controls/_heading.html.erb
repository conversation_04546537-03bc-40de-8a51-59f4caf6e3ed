<div class=" bg-base-100 border border-base-300 px-4 py-4 rounded-box">
  <label class="label label-sm font-normal">Nadpis sekce</label>
<div data-controller="heading" data-heading-control-id-value="<%= b.object.id %>">

  <div class="flex flex-col space-y-2">
    <div class="flex flex-col space-y-2">
      <div>
        <div class="rounded-md flex relative"
             data-controller="dropdown"
             data-action="click@window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide"
        >
          <button type="button" data-action="dropdown#toggle:stop" data-dropdown-target="button" style="height: 42px" class="flex items-center rounded-l space-x-0.5 bg-white text-black text-sm px-2 border-l border-gray-300 border-y">
            <span class="hidden selected-h1">H1</span>
            <span class="hidden selected-h2">H2</span>
            <span class="hidden selected-h3">H3</span>
            <span class="hidden selected-h4">H4</span>
            <span class="hidden selected-h5">H5</span>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
              <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
            </svg>
          </button>
          <div data-dropdown-target="menu" class="hidden absolute top-4 left-0 z-10 mt-5 flex w-screen max-w-max p-3 bg-white rounded border border-gray-300">
            <div class="text-sm overflow-hidden w-36">
              <label class='justify-between no-underline flex px-3 py-2 rounded text-gray-900 bg-white hover:bg-gray-100'>
                <div class="flex space-x-1.5">
                  <span>H1</span>
                </div>
                <%= b.radio_button :heading_type, "h1", { 'data-action': 'dropdown#change dropdown#toggle heading#onUpdateHeading', 'data-dropdown-target': 'input', class: "radio radio-xs" } %>
              </label>
              <label class='justify-between no-underline flex px-3 py-2 rounded text-gray-900 bg-white hover:bg-gray-100'>
                <div class="flex space-x-1.5">
                  <span>H2</span>
                </div>
                <%= b.radio_button :heading_type, "h2", { 'data-action': 'dropdown#change dropdown#toggle heading#onUpdateHeading', 'data-dropdown-target': 'input', class: "radio radio-xs" } %>
              </label>
              <label class='justify-between no-underline flex px-3 py-2 rounded text-gray-900 bg-white hover:bg-gray-100'>
                <div class="flex space-x-1.5">
                  <span>H3</span>
                </div>
                <%= b.radio_button :heading_type, "h3", { 'data-action': 'dropdown#change dropdown#toggle heading#onUpdateHeading', 'data-dropdown-target': 'input', class: "radio radio-xs" } %>
              </label>
              <label class='justify-between no-underline flex px-3 py-2 rounded text-gray-900 bg-white hover:bg-gray-100'>
                <div class="flex space-x-1.5">
                  <span>H4</span>
                </div>
                <%= b.radio_button :heading_type, "h4", { 'data-action': 'dropdown#change dropdown#toggle heading#onUpdateHeading', 'data-dropdown-target': 'input', class: "radio radio-xs" } %>
              </label>
              <label class='justify-between no-underline flex px-3 py-2 rounded text-gray-900 bg-white hover:bg-gray-100'>
                <div class="flex space-x-1.5">
                  <span>H5</span>
                </div>
                <%= b.radio_button :heading_type, "h5", { 'data-action': 'dropdown#change dropdown#toggle heading#onUpdateHeading', 'data-dropdown-target': 'input', class: "radio radio-xs" } %>
              </label>
            </div>
          </div>

          <div class="w-full h-auto">
            <div data-controller="tiptap" data-tiptap-preview-element-id-value="<%= "control-#{b.object.id}-heading" %>" class="text-base" data-tiptap-text-value="<%= b.object.text %>">
              <%= b.hidden_field :text, placeholder: "Text nadpisu", data: { 'tiptap-target': 'inputText' } %>
              <%= b.hidden_field :content, class: "input", value: b.object.content.to_json, data: { 'tiptap-target': 'inputContent' } %>

              <div data-tiptap-target="content" data-theme="<%= block.theme %>" class="text-black bg-white rounded-none rounded-r h-auto rounded border border-gray-300 p-2"></div>

              <%= render "/admin/content/blocks/controls/tiptap_toolbar", block_theme: block.theme %>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <%= b.text_field :pre_header, class: "input",  data: { pre_header_text_value: b.object.pre_header, action: 'heading#onUpdatePreHeader' }, placeholder: "Předtitulek" %>
    </div>
  </div>
</div>
</div>