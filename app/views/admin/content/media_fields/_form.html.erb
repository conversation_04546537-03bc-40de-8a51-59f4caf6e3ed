<%= form_with model: [:admin, :content, @media_type, @media_field], class: "space-y-6" do |f| %>
  <div class="bg-white shadow overflow-hidden rounded-lg">
    <div class="px-6 py-4 space-y-4">
      <div>
        <%= f.label :field_key, "Klíč pole", class: "block text-sm font-medium text-gray-700" %>
        <div class="mt-1">
          <%= f.text_field :field_key, class: "input", required: true %>
        </div>
        <p class="text-xs text-gray-500 mt-1"><PERSON><PERSON><PERSON><PERSON> pole musí být unikátní v rámci typu média a používá se pro přístup k hodnotě pole.</p>
        <% if @media_field.errors.messages_for(:field_key).any? %>
          <p class="mt-1 text-sm text-red-600"><%= @media_field.errors.messages_for(:field_key).join(", ") %></p>
        <% end %>
      </div>
      
      <div>
        <%= f.label :field_type, "Typ pole", class: "block text-sm font-medium text-gray-700" %>
        <div class="mt-1">
          <%= f.select :field_type, MediaField::FIELD_TYPES.map { |type| [type.humanize, type] }, {}, { class: "select", required: true } %>
        </div>
        <p class="text-xs text-gray-500 mt-1">Typ pole určuje, jaký formulářový prvek se zobrazí a jak se bude validovat.</p>
        <% if @media_field.errors.messages_for(:field_type).any? %>
          <p class="mt-1 text-sm text-red-600"><%= @media_field.errors.messages_for(:field_type).join(", ") %></p>
        <% end %>
      </div>
      
      <div>
        <%= f.label :position, "Pozice", class: "block text-sm font-medium text-gray-700" %>
        <div class="mt-1">
          <%= f.number_field :position, class: "input", min: 0, required: true %>
        </div>
        <p class="text-xs text-gray-500 mt-1">Pozice určuje pořadí polí ve formuláři.</p>
        <% if @media_field.errors.messages_for(:position).any? %>
          <p class="mt-1 text-sm text-red-600"><%= @media_field.errors.messages_for(:position).join(", ") %></p>
        <% end %>
      </div>
      
      <div>
        <div class="flex items-center">
          <%= f.check_box :required, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
          <%= f.label :required, "Povinné pole", class: "ml-2 block text-sm text-gray-900" %>
        </div>
        <p class="text-xs text-gray-500 mt-1">Povinná pole musí být vyplněna při vytváření nebo úpravě média.</p>
      </div>
      
      <div>
        <%= f.label :default_value, "Výchozí hodnota", class: "block text-sm font-medium text-gray-700" %>
        <div class="mt-1">
          <%= f.text_field :default_value, class: "input" %>
        </div>
        <p class="text-xs text-gray-500 mt-1">Výchozí hodnota pole při vytváření nového média.</p>
      </div>
    </div>
    
    <div class="px-6 py-3 bg-gray-50 text-right">
      <%= link_to "Zrušit", admin_content_media_type_path(@media_type), class: "btn btn-secondary mr-2" %>
      <%= f.submit "Uložit", class: "btn btn-primary" %>
    </div>
  </div>
<% end %>
