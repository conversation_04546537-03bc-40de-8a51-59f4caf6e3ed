<%= form_with model: [:admin, :content, @media_type], class: "space-y-6" do |f| %>
  <div class="bg-white shadow overflow-hidden rounded-lg">
    <div class="px-6 py-4 space-y-4">
      <div>
        <%= f.label :name, "Název", class: "block text-sm font-medium text-gray-700" %>
        <div class="mt-1">
          <%= f.text_field :name, class: "input", required: true %>
        </div>
        <% if @media_type.errors.messages_for(:name).any? %>
          <p class="mt-1 text-sm text-red-600"><%= @media_type.errors.messages_for(:name).join(", ") %></p>
        <% end %>
      </div>
      
      <div>
        <div class="flex items-center">
          <%= f.check_box :has_groups, class: "h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" %>
          <%= f.label :has_groups, "Podporuje skupiny", class: "ml-2 block text-sm text-gray-900" %>
        </div>
        <p class="text-xs text-gray-500 mt-1">Povolte tuto možnost, pokud tento typ média může být organizován do skupin.</p>
      </div>
    </div>
    
    <div class="px-6 py-3 bg-gray-50 text-right">
      <%= link_to "Zrušit", admin_content_media_types_path, class: "btn btn-secondary mr-2" %>
      <%= f.submit "Uložit", class: "btn btn-primary" %>
    </div>
  </div>
<% end %>
