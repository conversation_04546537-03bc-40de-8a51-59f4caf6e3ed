<div class="container mx-auto py-6">
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-2xl font-bold"><%= @media_type.name %></h1>
      <p class="text-gray-500">Typ média</p>
    </div>
    <div class="flex space-x-2">
      <%= link_to edit_admin_content_media_type_path(@media_type), class: "btn btn-secondary" do %>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
          <path d="M13.488 2.513a1.75 1.75 0 0 0-2.475 0L6.75 6.774a2.75 2.75 0 0 0-.596.892l-.848 2.047a.75.75 0 0 0 .98.98l2.047-.848a2.75 2.75 0 0 0 .892-.596l4.261-4.262a1.75 1.75 0 0 0 0-2.474Z" />
          <path d="M4.75 3.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h6.5c.69 0 1.25-.56 1.25-1.25V9A.75.75 0 0 1 14 9v2.25A2.75 2.75 0 0 1 11.25 14h-6.5A2.75 2.75 0 0 1 2 11.25v-6.5A2.75 2.75 0 0 1 4.75 2H7a.75.75 0 0 1 0 1.5H4.75Z" />
        </svg>
        <span>Upravit</span>
      <% end %>
      <%= link_to admin_content_media_types_path, class: "btn btn-secondary" do %>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
          <path fill-rule="evenodd" d="M14 8a.75.75 0 0 1-.75.75H4.56l2.22 2.22a.75.75 0 1 1-1.06 1.06l-3.5-3.5a.75.75 0 0 1 0-1.06l3.5-3.5a.75.75 0 0 1 1.06 1.06L4.56 7.25h8.69A.75.75 0 0 1 14 8Z" clip-rule="evenodd" />
        </svg>
        <span>Zpět</span>
      <% end %>
    </div>
  </div>
  
  <div class="bg-white shadow overflow-hidden rounded-lg mb-6">
    <div class="px-6 py-4">
      <h2 class="text-lg font-medium text-gray-900 mb-2">Detaily typu média</h2>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <p class="text-sm font-medium text-gray-500">Název</p>
          <p class="mt-1 text-sm text-gray-900"><%= @media_type.name %></p>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-500">Podporuje skupiny</p>
          <p class="mt-1 text-sm text-gray-900"><%= @media_type.has_groups ? "Ano" : "Ne" %></p>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-500">Počet polí</p>
          <p class="mt-1 text-sm text-gray-900"><%= @media_type.media_fields.count %></p>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-500">Počet médií</p>
          <p class="mt-1 text-sm text-gray-900"><%= @media_type.media.count %></p>
        </div>
      </div>
    </div>
  </div>
  
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-xl font-bold">Pole</h2>
    <%= link_to new_admin_content_media_type_media_field_path(@media_type), class: "btn btn-primary" do %>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
        <path d="M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 0 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z" />
      </svg>
      <span>Přidat pole</span>
    <% end %>
  </div>
  
  <div class="bg-white shadow overflow-hidden rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Klíč</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Typ</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Povinné</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pozice</th>
          <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Akce</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @media_fields.each do |field| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= field.field_key %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500"><%= field.field_type %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500"><%= field.required ? "Ano" : "Ne" %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500"><%= field.position %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <%= link_to "Upravit", edit_admin_content_media_type_media_field_path(@media_type, field), class: "text-indigo-600 hover:text-indigo-900 mr-3" %>
              <%= button_to "Smazat", admin_content_media_type_media_field_path(@media_type, field), method: :delete, class: "text-red-600 hover:text-red-900", form: { data: { turbo_confirm: "Opravdu chcete smazat toto pole?" } } %>
            </td>
          </tr>
        <% end %>
        
        <% if @media_fields.empty? %>
          <tr>
            <td colspan="5" class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
              Zatím nejsou vytvořena žádná pole pro tento typ média.
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
