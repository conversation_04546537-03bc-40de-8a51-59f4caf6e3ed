<div class="container mx-auto py-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold"><PERSON><PERSON> mé<PERSON></h1>
    <%= link_to new_admin_content_media_type_path, class: "btn btn-primary" do %>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
        <path d="M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 0 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z" />
      </svg>
      <span>Přidat typ média</span>
    <% end %>
  </div>
  
  <div class="bg-white shadow overflow-hidden rounded-lg">
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Název</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Počet polí</th>
          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Počet médií</th>
          <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Akce</th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200">
        <% @media_types.each do |media_type| %>
          <tr>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900"><%= media_type.name %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500"><%= media_type.media_fields.count %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-500"><%= media_type.media.count %></div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <%= link_to "Zobrazit", admin_content_media_type_path(media_type), class: "text-indigo-600 hover:text-indigo-900 mr-3" %>
              <%= link_to "Upravit", edit_admin_content_media_type_path(media_type), class: "text-indigo-600 hover:text-indigo-900 mr-3" %>
              <%= button_to "Smazat", admin_content_media_type_path(media_type), method: :delete, class: "text-red-600 hover:text-red-900", form: { data: { turbo_confirm: "Opravdu chcete smazat tento typ média? Tato akce smaže i všechna související pole." } } %>
            </td>
          </tr>
        <% end %>
        
        <% if @media_types.empty? %>
          <tr>
            <td colspan="4" class="px-6 py-4 whitespace-nowrap text-center text-sm text-gray-500">
              Zatím nejsou vytvořeny žádné typy médií.
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
