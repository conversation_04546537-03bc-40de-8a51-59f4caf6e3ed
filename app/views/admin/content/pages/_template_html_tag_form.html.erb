<li data-new-record="<%= f.object.new_record? %>" class="nested-form-wrapper flex items-center justify-between py-4 pl-4 pr-5 text-sm leading-6">
  <div class="flex w-0 flex-1 items-center">
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-5 w-5 flex-shrink-0 text-gray-400">
      <path stroke-linecap="round" stroke-linejoin="round" d="M17.25 6.75 22.5 12l-5.25 5.25m-10.5 0L1.5 12l5.25-5.25m7.5-3-4.5 16.5" />
    </svg>

    <div class="ml-4 flex min-w-0 flex-1 gap-2">
      <div class="flex font-medium items-center space-x-2">
        <%= f.text_field :name, placeholder: "<PERSON><PERSON>ze<PERSON>", class: "input" %>
        <%= f.text_field :content, placeholder: "Script", class: "input" %>
        <%= f.select :position, HtmlTag.positions.keys.map { |key| [HtmlTag::position_name(key), key] }, {}, class: 'select' %>
        <div class="ml-2">
          <button type="button" data-action="nested-form#remove"  class="text-red-500 p-1.5 hover:bg-red-50 rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
              <path d="M5.28 4.22a.75.75 0 0 0-1.06 1.06L6.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L8 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L9.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L8 6.94 5.28 4.22Z" />
            </svg>
          </button>
        </div>
        <%= f.hidden_field :_destroy %>
      </div>
    </div>
  </div>
</li>