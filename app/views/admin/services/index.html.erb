<h1><PERSON><PERSON><PERSON><PERSON><PERSON></h1>

<div class="flex flex-col sm:flex-row mt-6">
  <div class="w-full sm:w-[250px] hidden">
    <div class="bg-white shadow rounded w-full">
      <ul class="flex flex-col divide-y divide-gray-200">
        <li>
          <a href="#" class="flex justify-between p-2 text-gray-600 font-medium text-sm hover:bg-gray-50">
            <span>Aktivní</span>
            <span class="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">10</span>
          </a>
        </li>
        <li>
          <a href="#" class="flex justify-between p-2 text-gray-600 font-medium text-sm hover:bg-gray-50">
            <span>Vše</span>
            <span class="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">17</span>
          </a>
        </li>
        <li>
          <a href="#" class="flex justify-between p-2 text-gray-600 font-medium text-sm hover:bg-gray-50">
            <span>Marketing</span>
            <span class="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">4</span>
          </a>
        </li>
        <li>
          <a href="#" class="flex justify-between p-2 text-gray-600 font-medium text-sm hover:bg-gray-50">
            <span>Sortiment</span>
            <span class="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">1</span>
          </a>
        </li>
        <li>
          <a href="#" class="flex justify-between p-2 text-gray-600 font-medium text-sm hover:bg-gray-50">
            <span>Doprava / platba</span>
            <span class="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">2</span>
          </a>
        </li>
      </ul>
    </div>
  </div>

  <div class="overflow-hidden w-full mt-4 sm:mt-0">
    <ul role="list" class="grid grid-cols-2 sm:grid-cols-3 gap-3">
      <% Service::SERVICES.each do |service| %>
      <li>
        <a href="<%= edit_admin_service_path(service[:name].parameterize) %>" class="block bg-white border border-gray-200 hover:border-avocado-200">
          <div class="flex items-center px-4 py-4 sm:px-6">
            <div class="flex min-w-0 flex-1 items-center">
              <div class="flex-shrink-0">
                <span aria-label="Online" class="inline-block h-2 w-2 flex-shrink-0 rounded-full bg-green-400"></span>
              </div>
              <div class="ml-4">
                <img class="h-12 w-12 rounded-full p-2 shadow" src="/images/<%= service[:class_name].underscore + ".svg" %>">
              </div>
              <div class="min-w-0 flex-1 px-4">
                <div>
                  <p class="truncate text-sm font-medium text-black">
                    <%= service[:name] %>
                  </p>
                  <p class="w-full text-gray-500 text-xs">
                    <%= service[:label] %>
                  </p>
                </div>
              </div>
            </div>
            <div>
              <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
              </svg>
            </div>
          </div>
        </a>
      </li>
      <% end %>
    </ul>
  </div>
</div>