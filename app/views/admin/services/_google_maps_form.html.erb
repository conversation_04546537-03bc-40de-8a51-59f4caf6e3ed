<%= form_with model: @service, url: admin_service_path('google-maps') do |f| %>
  <div class="relative bg-white flex">
    <div class="flex-1 focus:outline-none">
      <div class="relative max-w-4xl mx-auto md:px-8 xl:px-0">
        <div class="py-10">
          <div class="px-4 sm:px-6 md:px-0 flex space-x-2 items-center">
            <a href="<%= admin_services_path %>" id="back-link" data-form-target="backLink">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3" />
              </svg>
            </a>

            <h1 class="text-2xl font-semibold text-gray-900">
              Propojení s Google Maps
            </h1>
          </div>

          <div class="mt-5">
            <div class="bg-white flex flex-col space-y-4">
              <div>
                <label>URL na google mapách</label>
                <div>
                  <%= f.text_field :place_url, name: :place_url, class: "input" %>
                </div>
              </div>
            </div>
          </div>

          <div class="mt-5">
            <div class="bg-white flex flex-col space-y-4">
              <label class="flex items-center space-x-2">
                  <button type="button"
                          class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:ring-offset-2"
                          role="switch"
                          aria-checked="false"
                          data-controller="toggle-checkbox"
                          data-action="click->toggle-checkbox#toggle"
                          data-turbo-permanent
                          data-toggle-checkbox-target="button">
                      <%= f.check_box :import_reviews, name: :import_reviews, class: "hidden", data: { toggle_checkbox_target: "checkbox" } %>
                      <span class="pointer-events-none relative inline-block h-5 w-5 translate-x-0 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                            data-toggle-checkbox-target="toggleSpan">
                        <span class="absolute inset-0 flex h-full w-full items-center justify-center opacity-100 transition-opacity duration-200 ease-in"
                              aria-hidden="true"
                              data-toggle-checkbox-target="iconOff">
                          <svg class="h-3 w-3 text-gray-400" fill="none" viewBox="0 0 12 12">
                            <path d="M4 8l2-2m0 0l2-2M6 6L4 4m2 2l2 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                          </svg>
                        </span>
                        <span class="absolute inset-0 flex h-full w-full items-center justify-center opacity-0 transition-opacity duration-100 ease-out"
                              aria-hidden="true"
                              data-toggle-checkbox-target="iconOn">
                          <svg class="h-3 w-3 text-indigo-600" fill="currentColor" viewBox="0 0 12 12">
                            <path d="M3.707 5.293a1 1 0 00-1.414 1.414l1.414-1.414zM5 8l-.707.707a1 1 0 001.414 0L5 8zm4.707-3.293a1 1 0 00-1.414-1.414l1.414 1.414zm-7.414 2l2 2 1.414-1.414-2-2-1.414 1.414zm3.414 2l4-4-1.414-1.414-4 4 1.414 1.414z" />
                          </svg>
                        </span>
                      </span>
                  </button>
                <span class="text-sm">Aktualizovat uživatelské recenze</span>
              </label>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
  <div class="py-3 bg-base-200/80 border-t-2 border-primary/50">
    <div class="max-w-4xl mx-auto">
      <button type="submit" class="button">Uložit nastavení</button>
    </div>
  </div>
<% end %>