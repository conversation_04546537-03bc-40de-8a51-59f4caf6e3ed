<%= form_with model: [:admin, pricing] do |f| %>
<div class="flex space-x-4 mt-4">
  <div class="relative w-full">
    <div class="flex-1 focus:outline-none bg-white w-full">
      <div class="relative mx-auto">
        <div class="">
          <div class="flex gap-x-5" data-tabs-target="panel">
            <div class="flex-1 order-last bg-white py-3">
                <div class="mb-2">
                  <div class="field">
                    <label class="label">Název ceníku</label>
                    <%= f.text_field :name, class: "input" %>
                  </div>
                </div>
            </div>

            <div class="px-2 py-2 bg-avocado-100">
              <ul class="flex flex-col" data-controller="sortable" data-sortable-animation-value="150" data-sortable-handle-value=".handle">
                <% @pricing.pricing_sections.each do |pricing_section| %>
                  <li class="flex space-x-2 px-2 items-center" data-sortable-update-url="<%= admin_pricing_pricing_section_path(@pricing, pricing_section.id) %>">
                    <span class="text-sm flex-1"><%= pricing_section.name %></span>

                    <div class="flex">
                      <%= link_to admin_pricing_pricing_section_path(@pricing, pricing_section.id), data: { turbo_confirm: "Opravdu chcete smazat sekci z ceníku včetně přidaných položek?", turbo_method: :delete }, class: "text-red-500 p-1.5 hover:bg-red-50 rounded-full" do %>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                          <path d="M5.28 4.22a.75.75 0 0 0-1.06 1.06L6.94 8l-2.72 2.72a.75.75 0 1 0 1.06 1.06L8 9.06l2.72 2.72a.75.75 0 1 0 1.06-1.06L9.06 8l2.72-2.72a.75.75 0 0 0-1.06-1.06L8 6.94 5.28 4.22Z" />
                        </svg>
                      <% end %>

                      <button class="handle text-gray-700 p-1.5 hover:text-gray-900 hover:bg-gray-100 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                          <path fill-rule="evenodd" d="M13.78 10.47a.75.75 0 0 1 0 1.06l-2.25 2.25a.75.75 0 0 1-1.06 0l-2.25-2.25a.75.75 0 1 1 1.06-1.06l.97.97V5.75a.75.75 0 0 1 1.5 0v5.69l.97-.97a.75.75 0 0 1 1.06 0ZM2.22 5.53a.75.75 0 0 1 0-1.06l2.25-2.25a.75.75 0 0 1 1.06 0l2.25 2.25a.75.75 0 0 1-1.06 1.06l-.97-.97v5.69a.75.75 0 0 1-1.5 0V4.56l-.97.97a.75.75 0 0 1-1.06 0Z" clip-rule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </li>
                <% end %>
              </ul>

              <div class="mt-3">
                <%= f.fields_for :pricing_sections, PricingSection.new do |pricing_section_fields| %>

                    <div class="grid grid-cols-1">
                      <%= pricing_section_fields.text_field :name, placeholder: "Přidat sekci", class: "col-start-1 row-start-1 block w-full rounded-md bg-white py-1.5 pl-3 pr-10 text-base text-gray-900 outline outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline focus:outline-2 focus:-outline-offset-2 focus:outline-black sm:pr-9 sm:text-sm/6" %>

                      <div class="col-start-1 row-start-1 mr-1.5 self-center justify-self-end text-gray-400 mt-1">
                      <%= pricing_section_fields.button nil, class: "cursor-pointer bg-avocado-500 hover:bg-avocado-600 text-white rounded-full p-1" do %>
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                          <path d="M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 0 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z" />
                        </svg>
                      <% end %>
                      </div>
                    </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="flex space-x-4 mt-4">
  <div class="relative w-full">
    <div class="flex-1 focus:outline-none bg-white w-full">
      <div class="relative mx-auto">
        <div data-tabs-target="panel">
          <div class="flex flex-col space-y-4">
            <% pricing.pricing_sections.each do |pricing_section| %>
              <%= f.fields_for :pricing_sections, pricing_section do |pricing_section_fields| %>
                <div data-controller="nested-form" data-nested_form_wrapper_selector_value=".nested-form-wrapper">
                  <template data-nested-form-target="template">
                    <%= pricing_section_fields.fields_for :pricing_items, PricingItem.new, child_index: 'NEW_RECORD' do |pricing_item_fields| %>
                      <%= render "dish_form", f: pricing_item_fields %>
                    <% end %>
                  </template>

                  <div class="bg-white">
                    <%= pricing_section_fields.text_field :name, class: "w-full flex bg-avocado-50 text-avocado-900 p-2 border-none font-medium text-sm" %>

                    <div class="pl-3 py-3">
                        <div class="flex items-center space-x-2 w-full mb-1.5">
                          <div class="w-1/4">Název položky</div>
                          <div class="w-24 text-sm">Cena</div>
                          <div class="w-24 text-sm">Cena EUR</div>
                          <div class="w-20 text-sm">Čas</div>
                          <div class="flex-grow text-sm">Popis</div>
                          <div></div>
                        </div>

                      <div class="flex flex-col space-y-2">
                        <%= pricing_section_fields.fields_for :pricing_items do |pricing_item_fields| %>
                          <%= render "dish_form", f: pricing_item_fields %>
                        <% end %>
                        <div data-nested-form-target="target"></div>
                      </div>

                      <button type="button" data-action="nested-form#add" class="flex items-center text-xs text-gray-700 p-1 mt-1 hover:bg-gray-100 rounded-md bg-gray-50 font-medium">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
                          <path d="M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 0 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z" />
                        </svg>

                        <span>Přidat položku do sekce <%= pricing_section.name %></span>
                      </button>
                    </div>
                  </div>
                </div>
              <% end %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

  <div class="py-3 bg-base-200/80 border-t-2 border-primary/50">
    <div class="px-3">
      <button type="submit" class="btn btn-primary">Uložit nastavení</button>
    </div>
  </div>
<% end %>