<div class="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true">
  <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

  <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
      <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
        <div class="absolute right-0 top-0 hidden pr-4 pt-4 sm:block">
          <a href="<%= admin_pricing_index_path %>" class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
            <span class="sr-only">Close</span>
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </a>
        </div>
        <%= form_with model: [:admin, @pricing], url: admin_pricing_index_path do |f| %>
          <div class="sm:flex sm:items-start" data-controller="create-menu" data-action="turbo:morph@window->create-menu#reconnect">
            <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-avocado-100 sm:mx-0 sm:h-10 sm:w-10">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6 text-avocado-600">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m3.75 9v6m3-3H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
              </svg>
            </div>
            <div class="grow mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
              <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">Přidat ceník</h3>
              <div class="mt-2 w-full">
                  <div class="flex flex-col space-y-3">
                  <div>
                    <label class="label">
                      Název ceníku
                    </label>
                    <div class="mt-1">
                      <%= f.text_field :name, class: "input" %>
                    </div>
                    <% if @pricing.errors.messages_for(:name).any? %>
                      Zadejte název ceníku
                    <% end %>
                  </div>

                  <div class="flex flex-col space-y-3">
                    <div>
                      <label class="label">
                        Typ ceníku
                      </label>
                      <div class="mt-1">
                        <%= f.select :pricing_type, [['Výchozí', nil], ['Týdenní', 'daily'], ['Sezónní', 'seasonal']], {}, { data: {'create-menu-target': "type", action: 'create-menu#handleTypeChange'}, class: "input" } %>
                      </div>
                    </div>

                    <div data-create-menu-target="validity" class="hidden">
                    <div>
                      <label class="label">
                        Platnost od:
                      </label>
                      <div class="mt-1">
                        <%= f.date_field :valid_from, class: "input", data: { 'create-menu-target': "validFrom"}  %>
                      </div>
                    </div>

                    <div class="mt-3">
                      <label class="label">
                        Platnost do:
                      </label>
                      <div class="mt-1">
                        <%= f.date_field :valid_to, class: "input", data: { 'create-menu-target': "validTo"}  %>
                      </div>
                  </div>
                      <div class="flex flex-wrap hidden grid-cols-2 gap-1 mt-1.5" data-create-menu-target="weeks">
                        <% [Date.today.beginning_of_week..Date.today.end_of_week,
                            1.week.from_now.beginning_of_week..1.week.from_now.end_of_week,
                            2.weeks.from_now.beginning_of_week..2.weeks.from_now.end_of_week,
                            3.weeks.from_now.beginning_of_week..3.weeks.from_now.end_of_week
                           ].each do |day| %>

                          <button type="button" data-action="create-menu#setWeek" data-valid-from="<%= day.first.strftime('%Y-%m-%d') %>" data-valid-to="<%= day.last.strftime('%Y-%m-%d') %>" class="inline-flex px-2 py-1.5 border rounded text-xs hover:bg-gray-50">
                            <%= day.first.strftime('%d.%m.%Y') %> - <%= day.last.strftime('%d.%m.%Y') %>
                          </button>
                        <% end %>
                      </div>
                    </div>
                  </div>
                  </div>
              </div>
            </div>
          </div>
          <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
            <button type="submit" class="inline-flex w-full justify-center rounded-md bg-avocado-500 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-avocado-600 sm:w-auto sm:ml-3">Přidat ceník</button>
            <a href="<%= admin_pricing_index_path %>" onclick="return confirm('opravdu chcete opustit formulář bez uložení?')" class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">Zrušit</a>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
