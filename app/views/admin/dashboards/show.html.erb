<div data-controller="blocks" data-page-id="1" class="p-4">
  <div id="blocks-container" class="border-2 border-gray-300 min-h-64 p-4 mb-4 bg-white shadow-md">
    <!-- Dynamically added blocks will be placed here -->
  </div>
  <div class="block-picker flex space-x-4">
    <div class="block bg-gray-200 p-4 border border-gray-400 cursor-pointer" data-action="dragstart->blocks#dragstart" draggable="true" data-block-type="contact">Contact Block</div>
    <div class="block bg-gray-200 p-4 border border-gray-400 cursor-pointer" data-action="dragstart->blocks#dragstart" draggable="true" data-block-type="hero">Hero Block</div>
    <div class="block bg-gray-200 p-4 border border-gray-400 cursor-pointer" data-action="dragstart->blocks#dragstart" draggable="true" data-block-type="text">Text Block</div>
  </div>
</div>