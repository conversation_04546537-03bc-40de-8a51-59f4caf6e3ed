<%= turbo_frame_tag :modal do %>
  <%= form_with model: @reservation, url: confirm_admin_reservation_path, method: :patch, data: { turbo_frame: :_top } do |f| %>
      <div class="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
          <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <div class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-xl sm:p-6">
              <div class="sm:flex sm:items-start">
                <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 text-green-500">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                  </svg>
                </div>
                <div class="absolute right-0 top-0">
                  <a href="<%= admin_reservations_path %>" data-turbo-method="get" data-turbo-confirm="Opravdu?" class="inline-flex px-2 py-1.5 bg-gray-50">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12" />
                    </svg>
                  </a>
                </div>
                <div class="w-full sm:ml-3 mt-3 text-center sm:mt-0 sm:text-left">
                  <h3 class="text-base font-semibold leading-6 text-gray-900 bg-green-100 p-2" id="modal-title">
                    Potvrzení rezervace <%= l @reservation.date, format: :date %> v <%= @reservation.time.strftime("%H:%M") %> hodin
                  </h3>
                  <div class="my-2">
                    <table class="w-full border border-avocado-50 mt-2">
                      <tr class="bg-avocado-50">
                        <td class="p-1 border-r">Jméno:</td>
                        <td class="p-1"><%= @reservation.name %></td>
                      </tr>
                      <tr>
                        <td class="p-1 border-r">E-mail:</td>
                        <td class="p-1"><%= @reservation.email %></td>
                      </tr>
                      <tr class="bg-avocado-50">
                        <td class="p-1 border-r">Telefon:</td>
                        <td class="p-1"><%= @reservation.phone %></td>
                      </tr>
                      <tr>
                        <td class="p-1 border-r">Počet osob:</td>
                        <td class="p-1"><%= @reservation.guests %> osob</td>
                      </tr>
                    </table>
                    <% if @reservation.note.present? %>
                      <p class="bg-avocado-50 text-sm p-2"><%= @reservation.note %></p>
                    <% end %>
                  </div>

                  <% if current_tenant.sms_on_reservation_confirm? %>
                    <div>
                      <label for="send_sms" class="text-sm text-gray-700 checkbox">
                        <%= f.check_box :send_sms, checked: true %>
                        Odeslat SMS?
                      </label>
                      <textarea class="w-full form-input" rows="2"><%= t("reservations.confirm.sms", guests: @reservation.guests, website: current_tenant.name, time: I18n.l(@reservation.time, format: :time), date: I18n.l(@reservation.date, format: :short_date)) %></textarea>
                    </div>
                  <% end %>

                  <div class="mt-3">
                    <label for="send_email" class="text-sm text-gray-700 checkbox">
                      <%= f.check_box :send_email, checked: true %>
                      Odeslat E-mail?
                    </label>
                    <textarea class="w-full form-input" rows="7"><%= t("reservations.confirm.email", guests: @reservation.guests, website: current_tenant.name, time: I18n.l(@reservation.time, format: :time), date: I18n.l(@reservation.date, format: :short_date)) %></textarea>
                  </div>

                  <div class="mt-3  sm:mt-4 sm:flex border-t pt-3">
                    <button type="submit" class="button">
                      Potvrdit rezervaci
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    <% end %>
<% end %>