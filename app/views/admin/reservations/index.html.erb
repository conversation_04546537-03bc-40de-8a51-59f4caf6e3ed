<%= content_for :action_toolbar do %>
  <a href="<%= new_admin_reservation_path %>" class="button">
    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
      <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
    </svg>
    Vytvořit rezervaci
  </a>
<% end %>

<%= turbo_frame_tag :modal %>

<%= render 'calendar' %>

<h1>Rezervace</h1>

<div class="mt-6 flow-root border">
  <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
      <table class="min-w-full divide-y divide-gray-300">
        <thead class="bg-white">
        <tr class="divide-x divide-gray-200">
          <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Zákazník</th>
          <th scope="col" class="px-3 py-3.5 text-sm font-semibold text-gray-900 text-left">Rezervace</th>
          <th scope="col" class="py-3.5 pl-4 pr-3 text-center text-sm font-semibold text-gray-900">Stav</th>
          <th scope="col" class="py-3.5 pl-4 pr-3 text-center text-sm font-semibold text-gray-900">
            Akce
          </th>
        </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 bg-white">
        <% @reservations.each do |reservation| %>
          <tr class="divide-x divide-gray-200 <%= reservation.pending? ? "bg-yellow-50" : "" %>">
            <td class="py-2 px-5 text-sm">
              <div class="flex flex-col space-y-1">
                <div class="sm:font-medium text-gray-900"><%= reservation.name %></div>
                <%= content_tag(:div, reservation.phone, class: "sm:font-medium text-gray-900") if reservation.phone.present?  %>
                <%= content_tag(:div, reservation.email, class: "sm:font-medium text-gray-900") if reservation.email.present? %>
              </div>
            </td>
            <td class="px-5 py-5 text-sm">
              <div class="flex flex-col space-y-1 justify-center">
                <div class="flex items-center space-x-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5 text-gray-400">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z" />
                  </svg>

                  <span><%= day_name(reservation.date) %> <%= reservation.date %></span>
                </div>

                <div class="flex items-center space-x-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5 text-gray-400">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                  </svg>

                  <span><%= reservation.time.strftime("%H:%M") %> hodin</span>
                </div>

                <div class="flex items-center space-x-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5 text-gray-400">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z" />
                  </svg>

                  <span><%= reservation.guests %> osob</span>
                </div>

                <% if reservation.note.present? %>
                  <div class="flex items-center space-x-2">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5 text-gray-400">
                      <path stroke-linecap="round" stroke-linejoin="round" d="M8.625 12a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H8.25m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0H12m4.125 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 0 1-2.555-.337A5.972 5.972 0 0 1 5.41 20.97a5.969 5.969 0 0 1-.474-.065 4.48 4.48 0 0 0 .978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25Z" />
                    </svg>

                    <p class="text-sm"><%= reservation.note %></p>
                  </div>
                <% end %>
              </div>
            </td>
            <td class="w-1 px-3 py-5 text-sm text-gray-500">
              <div class="flex flex-col justify-center">
                <% if reservation.pending? %>
                  <div class="text-center whitespace-nowrap rounded-md bg-orange-200 px-2 py-1 text-xs font-medium text-orange-700 ring-1 ring-inset ring-orange-400">
                    Čeká na potvrzení
                  </div>

                  <div class="flex mt-2">
                    <%= link_to confirm_form_admin_reservation_path(reservation), data: { turbo_frame: 'modal' }, class: "bg-green-600 text-sm hover:bg-green-700 py-1 px-1.5 text-white rounded-l" do %>
                      Potvrdit
                    <% end %>

                    <%= link_to cancel_form_admin_reservation_path(reservation), data: { turbo_frame: 'modal' }, class: "bg-red-600 text-sm hover:bg-red-700 py-1 px-1.5 text-white rounded-r" do %>
                      Zamítnout
                    <% end %>
                  </div>
                <% elsif reservation.confirmed?%>
                  <div class="mx-auto">
                   <span class="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20">Schváleno</span>
                  </div>
                <% else %>
                  <div class="mx-auto">
                    <span class="inline-flex items-center rounded-md bg-red-50 px-2 py-1 text-xs font-medium text-red-700 ring-1 ring-inset ring-red-600/20">Zamítnuto</span>
                  </div>
                <% end %>
              </div>
            </td>
            <td class="w-1 relative whitespace-nowrap py-5 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
              <div class="px-6 py-1">
                <span class="flex space-x-2 text-right">
                  <%= link_to edit_admin_reservation_path(reservation), data: { tippy_content: 'Upravit stránku' }, class: 'p-1 hover:bg-avocado-50 rounded-full' do %>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-avocado-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  <% end %>
                </span>
              </div>
            </td>
          </tr>
        <% end %>
        </tbody>
      </table>
    </div>
  </div>
</div>

<h1 class="mt-4 hidden">Kalendář</h1>