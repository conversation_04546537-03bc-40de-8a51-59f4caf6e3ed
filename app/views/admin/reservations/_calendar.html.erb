<div class="flex h-full flex-col calendar-container">
  <header class="flex flex-none items-center justify-between border-b border-gray-200 px-6 py-4">
    <div>
      <h1 class="text-base font-semibold leading-6 text-gray-900">
        <time datetime="<%= Date.current %>" class="sm:hidden"><%= l Date.current, format: :short %></time>
        <time datetime="<%= Date.current %>" class="hidden sm:inline"><%= l Date.current, format: :short %></time>
      </h1>
      <p class="mt-1 text-sm text-gray-500"><%= l Date.current, format: :day_name%> </p>
    </div>
    <div class="flex items-center">
      <div class="relative flex items-center rounded-md bg-white shadow-sm md:items-stretch">
        <button type="button" class="flex h-9 w-12 items-center justify-center rounded-l-md border-y border-l border-gray-300 pr-1 text-gray-400 hover:text-gray-500 focus:relative md:w-9 md:pr-0 md:hover:bg-gray-50">
          <span class="sr-only">Previous day</span>
          <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
          </svg>
        </button>
        <button type="button" class="hidden border-y border-gray-300 px-3.5 text-sm font-semibold text-gray-900 hover:bg-gray-50 focus:relative md:block">
          Dnes
        </button>
        <span class="relative -mx-px h-5 w-px bg-gray-300 md:hidden"></span>
        <button type="button" class="flex h-9 w-12 items-center justify-center rounded-r-md border-y border-r border-gray-300 pl-1 text-gray-400 hover:text-gray-500 focus:relative md:w-9 md:pl-0 md:hover:bg-gray-50">
          <span class="sr-only">Next day</span>
          <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
      <div class="relative ml-6 md:hidden">
        <button type="button" class="-mx-2 flex items-center rounded-full border border-transparent p-2 text-gray-400 hover:text-gray-500" id="menu-0-button" aria-expanded="false" aria-haspopup="true">
          <span class="sr-only">Open menu</span>
          <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path d="M3 10a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zM8.5 10a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zM15.5 8.5a1.5 1.5 0 100 3 1.5 1.5 0 000-3z" />
          </svg>
        </button>

        <div class="hidden absolute right-0 z-10 mt-3 w-36 origin-top-right divide-y divide-gray-100 overflow-hidden rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none" role="menu" aria-orientation="vertical" aria-labelledby="menu-0-button" tabindex="-1">
          <div class="py-1" role="none">
            <!-- Active: "bg-gray-100 text-gray-900", Not Active: "text-gray-700" -->
            <a href="#" class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1" id="menu-0-item-0">Create event</a>
          </div>
          <div class="py-1" role="none">
            <a href="#" class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1" id="menu-0-item-1">Go to today</a>
          </div>
          <div class="py-1" role="none">
            <a href="#" class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1" id="menu-0-item-2">Day view</a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1" id="menu-0-item-3">Week view</a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1" id="menu-0-item-4">Month view</a>
            <a href="#" class="block px-4 py-2 text-sm text-gray-700" role="menuitem" tabindex="-1" id="menu-0-item-5">Year view</a>
          </div>
        </div>
      </div>
    </div>
  </header>
  <div class="isolate flex flex-auto overflow-hidden bg-white" style="max-height: 600px;">
    <div class="flex flex-auto flex-col overflow-auto">
      <div class="sticky top-0 z-10 grid flex-none grid-cols-7 bg-white text-xs text-gray-500 shadow ring-1 ring-black ring-opacity-5 md:hidden">
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>W</span>
          <!-- Default: "text-gray-900", Selected: "bg-gray-900 text-white", Today (Not Selected): "text-indigo-600", Today (Selected): "bg-indigo-600 text-white" -->
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full text-base font-semibold text-gray-900">19</span>
        </button>
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>T</span>
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full text-base font-semibold text-indigo-600">20</span>
        </button>
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>F</span>
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full text-base font-semibold text-gray-900">21</span>
        </button>
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>S</span>
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full bg-gray-900 text-base font-semibold text-white">22</span>
        </button>
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>S</span>
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full text-base font-semibold text-gray-900">23</span>
        </button>
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>M</span>
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full text-base font-semibold text-gray-900">24</span>
        </button>
        <button type="button" class="flex flex-col items-center pb-1.5 pt-3">
          <span>T</span>
          <span class="mt-3 flex h-8 w-8 items-center justify-center rounded-full text-base font-semibold text-gray-900">25</span>
        </button>
      </div>
      <div class="flex w-full flex-auto">
        <div class="w-14 flex-none bg-white ring-1 ring-gray-100"></div>
        <div class="grid flex-auto grid-cols-1 grid-rows-1">
          <!-- Horizontal lines -->
          <div class="col-start-1 col-end-2 row-start-1 grid divide-y divide-gray-100" style="grid-template-rows: repeat(48, minmax(3.5rem, 1fr))">
            <div class="row-end-1 h-7"></div>
            <% (0..23).each do |hour| %>
              <div class="calendar-hour hour-<%= hour %>">
                <div class="-ml-14 -mt-2.5 w-14 pr-2 font-bold text-right text-xs leading-5 text-black"><%= Time.parse("#{hour}:00").strftime("%H:%M") %></div>
              </div>
              <div class="calendar-hour"></div>
            <% end %>
          </div>

          <!-- Events -->
          <ol class="col-start-1 col-end-2 row-start-1 grid" style="grid-template-columns: repeat(8, 1fr); grid-template-rows: 1.75rem repeat(288, minmax(0, 1fr)) auto;">
            <% ongoing_reservations = [] %>

            <% @reservations.order(id: :desc).sort_by(&:time).each do |reservation| %>
              <% start_minutes = reservation.start_time.hour * 60 + reservation.start_time.min %>
              <% end_minutes = start_minutes + reservation.duration %>
              <% start_row = (start_minutes / 5) + 2 %>
              <% span_rows = (reservation.duration / 5) %>

              <!-- Remove ongoing reservations that have ended -->
              <% ongoing_reservations.reject! { |_, end_time| end_time <= start_minutes } %>

              <!-- Calculate the next available column index -->
              <% used_columns = ongoing_reservations.map(&:first) %>
              <% current_column = (1..8).find { |col| !used_columns.include?(col) } %>

              <!-- Add this reservation to the ongoing reservations list -->
              <% ongoing_reservations << [current_column, end_minutes] %>

              <li class="relative mt-px flex" style="grid-row: <%= start_row %> / span <%= span_rows %>; grid-column: <%= current_column %> / span 1;">
                <a href="#" class="group absolute inset-1 flex flex-col overflow-y-auto rounded-lg bg-blue-50 p-2 text-xs leading-5 hover:bg-blue-100">
                  <p class="order-1 font-semibold text-blue-700"><%= reservation.name %></p>
                  <p class="text-blue-500 group-hover:text-blue-700"><time datetime="<%= reservation.start_time.iso8601 %>"><%= reservation.start_time.strftime("%-I:%M %p") %></time></p>
                </a>
              </li>
            <% end %>
          </ol>

        </div>
      </div>
    </div>
  </div>
</div>
