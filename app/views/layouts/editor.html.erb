<!DOCTYPE html>
<html>
<head>
  <title>Editor obsahu | <%= current_tenant.name %></title>
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <%= turbo_refreshes_with method: :morph, scroll: :preserve %>
  <%= yield :head %>
  <%= csrf_meta_tags %>
  <%= csp_meta_tag %>

  <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
  <%= javascript_include_tag "admin", "data-turbo-track": "reload", type: "module" %>

  <style>
      .ProseMirror {
          outline: none;
      }

      .hw-combobox__main__wrapper {
          width: 100% !important;
      }

      .hw-combobox__main__wrapper:focus-within {
          outline: 2px solid black;
      }

      .hw-combobox {
          width: 100% !important;
      }

      .hw-combobox__main__wrapper {
          width: 100% !important;
          border-radius: 0 5px 5px 0 !important;
          background: #fff;
      }

       .hw-combobox__input {
          background: transparent !important;
      }
  </style>

  <style>
      .image-small {
          width: 100px; /* Adjust size as needed */
      }

      .image-large {
          width: 500px; /* Adjust size as needed */
      }

      .image-full {
          width: 100%; /* Full width */
      }

      .slider-styled,
      .slider-styled .noUi-handle {
          box-shadow: none;
      }

      /* Hide markers on slider handles */
      .slider-styled .noUi-handle::before,
      .slider-styled .noUi-handle::after {
          display: none;
      }

      .slider-round {
          height: 10px !important;
      }

      .slider-round .noUi-connect {
          background: #000000 !important;
      }

      .slider-round .noUi-handle {
          height: 18px !important;
          width: 18px !important;
          top: -5px !important;
          right: -9px !important; /* half the width */
          border-radius: 9px !important;
      }

      .slider-styled .noUi-tooltip {
          display: none;
      }
      .slider-styled .noUi-active .noUi-tooltip {
          display: block;
          font-size: 12px;
          padding: 0 5px;
          background: black;
          color: white;
          border: none;
          border-radius: 5px;
      }

      /* Dropdown se zobrazením ikon */
      .dropdown-content {
          position: absolute;
          top: 100%;
          left: 0;
          background: #fff;
          border: 1px solid #ccc;
          padding: 0.5rem;
          border-radius: 0.25rem;
          z-index: 9999999;
          display: none;
          min-width: 100%;
      }
      .dropdown-content.active {
          display: grid;
      }
  </style>

  <style id="styles">
      :root {
      <%= generate_color_variables_from_yaml(@colors) %>
          --custom-radius-selector: <%= @theme[:radius][:selector] %>;
          --custom-radius-field: <%= @theme[:radius][:field] %>;
          --custom-radius-box: <%= @theme[:radius][:box] %>;
          --custom-size-selector: <%= @theme[:sizes][:selector] %>;
          --custom-size-field: <%= @theme[:sizes][:field] %>;
          --custom-options-border: <%= @theme[:options][:border] %>;
          --custom-options-depth: <%= @theme[:options][:depth] %>;
          --custom-options-noise: <%= @theme[:options][:noise] %>;
          --decorative-font: "Dancing Script", cursive;
      }
  </style>

  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.8.1/nouislider.min.css">
  <link href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css" rel="stylesheet">

  <%= combobox_style_tag %>
</head>

<body data-theme="admin" data-controller="mobile-preview">

<button data-action="click->mobile-preview#togglePreview" class="hidden md:block fixed top-0 right-0 z-50">
  Přepnout na mobilní náhled
</button>

<div data-mobile-preview-target="iframeContainer" style="position: relative;">
  <!-- iframe se sem načte -->
</div>

<div data-mobile-preview-target="mainContent">
  <%= render "shared/header", page_nodes: pages %>

  <%= yield %>

  <%= render "shared/footer", page_nodes: pages %>
</div>

<style>
    .radio-label {
        display: flex;
        align-items: center;
        border: 1px solid #ccc;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .radio-label:hover {
        background-color: #f0f0f0;
    }

    .radio-button {
        display: none;
    }

    .button-label {
        padding: 2px 8px;
    }

    .radio-button:checked + .button-label {
        background-color: #007bff;
        color: white;
        border-radius: 4px;
    }

</style>
</body>
</html>
