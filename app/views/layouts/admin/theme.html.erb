<%= content_for :sub_navigation do %>
  <ul id="sub-navigation" class="flex mt-4 flex-col text-sm font-medium text-gray-600 list-disc list-inside" data-turbo-permanent>
    <li data-tabs-target="tab" id="logo" data-action="click->tabs#change:prevent">
      <a href="#" class="pl-4 py-2 block text-gray-600" data-id="logo" data-action="keydown.left->tabs#previousTab keydown.right->tabs#nextTab keydown.home->tabs#firstTab:prevent keydown.end->tabs#lastTab:prevent">
        Logo
      </a>
    </li>
    <li data-tabs-target="tab" id="default" data-action="click->tabs#change:prevent">
      <a href="#" class="pl-4 py-2 block text-gray-600" data-id="default" data-action="keydown.left->tabs#previousTab keydown.right->tabs#nextTab keydown.home->tabs#firstTab:prevent keydown.end->tabs#lastTab:prevent">
        Obecné
      </a>
    </li>
    <li data-tabs-target="tab" id="nav" data-action="click->tabs#change:prevent">
      <a href="#" class="pl-4 py-2 block text-gray-600" data-id="nav" data-action="keydown.left->tabs#previousTab keydown.right->tabs#nextTab keydown.home->tabs#firstTab:prevent keydown.end->tabs#lastTab:prevent">
        Hlavní navigace
      </a>
    </li>
    <li data-tabs-target="tab" id="additional" data-action="click->tabs#change:prevent">
      <a href="#" class="pl-4 py-2 block text-gray-600" data-id="additional" data-action="keydown.left->tabs#previousTab keydown.right->tabs#nextTab keydown.home->tabs#firstTab:prevent keydown.end->tabs#lastTab:prevent">
        Additional Tab
      </a>
    </li>
  </ul>
<% end %>

<%= render template: "layouts/admin" %>
