<!DOCTYPE html>
<html lang="<%= I18n.locale %>">
  <head>
    <%= display_meta_tags site: nil, reverse: true %>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Dynamické načítání fontů podle uživatelského nastavení -->
    <%= google_fonts_link %>

    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    <%= javascript_include_tag "application", "data-turbo-track": "reload", layout: "module" %>

    <link href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css" rel="stylesheet">

    <style id="styles">
        :root {
            <% if current_tenant&.theme&.any? %>
            /* Uživatelské nastavení tématu z databáze */
            <%= generate_theme_css_variables %>
            <% else %>
            /* Výchozí nastavení z YAML */
            <%= generate_color_variables_from_yaml(@colors) %>
            <% end %>

            --custom-radius-selector: <%= @theme[:radius][:selector] %>;
            --custom-radius-field: <%= @theme[:radius][:field] %>;
            --custom-radius-box: <%= @theme[:radius][:box] %>;
            --custom-size-selector: <%= @theme[:sizes][:selector] %>;
            --custom-size-field: <%= @theme[:sizes][:field] %>;
            --custom-options-border: <%= @theme[:options][:border] %>;
            --custom-options-depth: <%= @theme[:options][:depth] %>;
            --custom-options-noise: <%= @theme[:options][:noise] %>;
            --decorative-font: "Dancing Script", cursive;
        }

        /* Aplikace uživatelských nastavení pro DaisyUI */
        <% if current_tenant&.theme&.any? %>
        body {
            font-family: var(--theme-font-family, <%= @template_settings&.dig(:font_family) || 'Inter, system-ui, sans-serif' %>);
        }

        h1, h2, h3, h4, h5, h6, .heading {
            font-family: var(--theme-heading-font-family, var(--theme-font-family, <%= @template_settings&.dig(:heading_font_family) || @template_settings&.dig(:font_family) || 'Inter, system-ui, sans-serif' %>));
        }
        <% end %>
    </style>

    <%# @template.template_html_tags.where(position: 0).each do |html_tag| %>
      <%#= html_tag.content.html_safe %>
    <%# end %>

  </head>

  <body class="flex flex-col min-h-screen">
    <%= render_header %>

    <div class="flex-1">
      <%= yield %>
    </div>

    <%= render "shared/footer_with_map", page_nodes: pages %>

    <%# @template.template_html_tags.where(position: 1).each do |html_tag| %>
    <%#= html_tag.content.html_safe %>
  <%# end %>
  </body>
</html>