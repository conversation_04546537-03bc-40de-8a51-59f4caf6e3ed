<% page_nodes.each do |page, children| %>
  <% if children.any? %>
    <div class="relative" data-controller="dropdown" data-action="click@window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide">
      <button data-action="dropdown#toggle:stop" type="button" class="flex items-center btn btn-ghost hover:bg-base-300" aria-expanded="false">
        <%= page.title %>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
          <path fill-rule="evenodd" d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z" clip-rule="evenodd" />
        </svg>
      </button>

      <div data-dropdown-target="menu" class="hidden relative z-50">
        <div class="absolute -left-24 top-full z-10 mt-3 menu bg-base-200 rounded-box w-56 bg-white">
          <% children.each do |child_page, child_pages| %>
            <div class="relative px-2 py-1.5 rounded-box hover:bg-gray-100" data-dropdown-target="menuItem">
              <a href="<%= page_path_resolver(child_page) %>" class="block text-sm font-medium leading-6 text-black">
                <%= child_page.title %>
                <span class="absolute inset-0"></span>
              </a>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  <% else %>
    <%= link_to page.title, page_path_resolver(page),
                class: (page.cta? ? 'btn btn-accent' : 'btn btn-ghost hover:bg-base-300'),
                target: ('_blank' if page.is_link?) %>
  <% end %>
<% end %>
