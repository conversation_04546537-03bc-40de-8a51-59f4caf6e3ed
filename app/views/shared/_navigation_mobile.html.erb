<div
  data-controller="dropdown"
  data-action="click@window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide"
  class="relative lg:hidden">

  <button data-action="dropdown#toggle:stop" type="button" class="text-xs flex flex-col -m-2.5 inline-flex items-center justify-center rounded-md p-2.5 text-gray-700">
    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
      <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
    </svg>
    <span class="uppercase font-medium">Menu</span>
  </button>

    <div data-dropdown-target="menu" class="hidden w-full fixed inset-y-0 right-0 z-10 flex flex-col justify-between overflow-y-auto bg-white sm:max-w-sm sm:ring-1 sm:ring-gray-900/10">
      <div class="p-6">
        <div class="flex items-center justify-between">
          <a href="<%= root_path %>" class="space-x-2 inline-flex items-center -m-1.5 p-1.5 text-lg font-medium">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
            </svg>

            <span>Menu</span>
          </a>
          <button data-action="dropdown#toggle:stop" type="button" class="-m-2.5 rounded-md p-2.5 text-gray-700 hover:bg-gray-50">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="mt-3 flow-root border-t pt-3">
          <div class="divide-y divide-gray-500/10">
            <div class="py-3">
              <% page_nodes.each do |page, children| %>
                <a href="<%= page_path_resolver(page) %>" data-dropdown-target="menuItem" class="group flex items-center px-2 py-3 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50">
                  <%= page.title %>
                </a>

                <% if children.any? %>
                  <% children.each do |child_page, child_pages| %>
                      <a href="<%= page_path_resolver(child_page) %>" class="flex space-x-2 items-center w-full group px-2 py-3 text-base font-semibold leading-7 text-gray-900 hover:bg-gray-50" data-dropdown-target="menuItem">
                      <span class="w-8 h-1 bg-gray-100"></span>
                      <span class="">
                        <%= child_page.title %>
                      </span>
                      </a>
                  <% end  %>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>