class BaseComponent < ApplicationComponent
  include Ui

  attr_reader :block_object, :block_presenter

  def initialize(block_object)
    @block_object = block_object
    @block_presenter = block_object
  end

  def container(css: nil, style: nil, &block)
    Ui::OuterContainer(block_object.id, block_object.outer_container_layer, css:, style:, &block)
  end

  def media_position
    "first"
  end

  def overlay
    Ui::Overlay(block_object.id, block_object.outer_container_layer)
  end

  def inner_container(css: nil, &block)
    Ui::InnerContainer(block_object.id, block_object.inner_container_layer, css:, &block)
  end

  def content_container(controls, css: nil, &block)
    Ui::ContentContainer(controls, block_object.id, block_object.content_layer, css: css, &block)
  end

  def media_container(css: nil, &block)
    Ui::MediaContainer(block_object, css: css, &block)
  end
end
