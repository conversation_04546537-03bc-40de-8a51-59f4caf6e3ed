module BlockControls
  class HeadingControl < Phlex::HTML
    include Phlex::Rails::Helpers::Tag

    register_element :block_control

    def initialize(heading_block_control_object)
      @control = heading_block_control_object
    end

    def view_template
        block_control(
          id: %(control-#{@control.id}),
          class: "control-container"
        ) do
          div do
            span(class: "text-base #{"hidden" if @control.pre_header&.empty?}", id: %(control-#{@control.id}-pre-header)) { @control.pre_header }
            public_send(@control.heading_type, class: "#{@control.heading_type}") do
              div(class: "inline-block", id: %(control-#{@control.id}-heading)) { @control.text.html_safe }
            end
          end
        end
    end
  end
end
