class BlockControls::FormControl < ApplicationComponent
  include Phlex::Rails::Helpers::EmailField
  include Phlex::Rails::Helpers::FormWith
  include Phlex::Rails::Helpers::NumberField
  include Phlex::Rails::Helpers::TelephoneField
  include Phlex::Rails::Helpers::TextArea
  include Phlex::Rails::Helpers::TextField
  include Phlex::Rails::Helpers::TurboFrameTag

  def initialize(control)
    @control = control
    @inbox_message ||= InboxMessage.new
  end

  def view_template
    form_with model: @inbox_message, id: "form" do |f|
      plain f.hidden_field :form_id, value: @control.form_id
      div(class: "flex items-center justify-center") do
        div(class: "flex flex-col space-y-4 w-full") do
          div(class: "w-full") do
            label(class: "label") { " E-mail " }
            plain f.email_field :email, class: "text-field"
          end

          div(class: "w-full") do
            label(class: "label") { " Telefon " }
            plain f.phone_field :phone, class: "text-field"
          end

          div(class: "w-full") do
            label(class: "label") { " Zpráva " }
            plain f.text_area :message, class: "text-field"
          end

          div do
            button(
              layout: "submit",
              class:
                "rounded-md bg-primary hover:bg-primary/90 py-3 px-4 text-center text-base font-semibold text-white outline-none"
            ) { " Odeslat zprávu " }
          end
        end
      end
    end
  end
end
