class BlockControls::ContactFormControl < ApplicationComponent
  include Phlex::Rails::Helpers::EmailField
  include Phlex::Rails::Helpers::FormWith
  include Phlex::Rails::Helpers::NumberField
  include Phlex::Rails::Helpers::TelephoneField
  include Phlex::Rails::Helpers::TextArea
  include Phlex::Rails::Helpers::TextField
  include Phlex::Rails::Helpers::TurboFrameTag
  def initialize(control)
    @control = control
    @inbox_message ||= InboxMessage.new
    @contact_form ||= Form.where(form_type: 0).take
  end

  def view_template
    form_with model: @inbox_message, id: "contact_form", class: "lg:flex-auto" do |f|
      plain f.hidden_field :form_id, value: @contact_form.id
      div(class: "grid grid-cols-1 gap-x-8 gap-y-6 sm:grid-cols-2") do
        div do
          label(
            class: "block text-sm font-semibold leading-6 text-gray-900"
          ) { "E-mail" }
          div(class: "mt-2.5") do
            plain f.email_field :email, class: "block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary sm:text-sm sm:leading-6"
          end
        end
        div do
          label(
            class: "block text-sm font-semibold leading-6 text-gray-900"
          ) { "Telefon" }
          div(class: "mt-2.5") do
            plain f.phone_field :phone, class: "block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary sm:text-sm sm:leading-6"
          end
        end
        div(class: "sm:col-span-2") do
          label(
            class: "block text-sm font-semibold leading-6 text-gray-900"
          ) { "Jméno a příjmení" }
          div(class: "mt-2.5") do
            plain f.text_field :name, class: "block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary sm:text-sm sm:leading-6"
          end
        end
        div(class: "sm:col-span-2") do
          label(
            for: "message",
            class: "block text-sm font-semibold leading-6 text-gray-900"
          ) { "Zpráva" }
          div(class: "mt-2.5") do
            f.text_area :message, class: "block w-full rounded-md border-0 px-3.5 py-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary sm:text-sm sm:leading-6"
          end
        end
      end
      div(class: "mt-10") do
        button(
          layout: "submit",
          class:
            "block w-full rounded-md bg-primary px-3.5 py-2.5 text-center text-sm font-semibold text-white shadow-sm hover:bg-primary/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600"
        ) { "Odeslat dotaz" }
      end
    end
  end
end
