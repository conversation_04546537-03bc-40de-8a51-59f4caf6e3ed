class BlockControls::ReservationFormControl < ApplicationComponent
  include Phlex::Rails::Helpers::EmailField
  include Phlex::Rails::Helpers::FormWith
  include Phlex::Rails::Helpers::NumberField
  include Phlex::Rails::Helpers::TelephoneField
  include Phlex::Rails::Helpers::TextArea
  include Phlex::Rails::Helpers::TextField
  include Phlex::Rails::Helpers::TurboFrameTag
  include Phlex::Rails::Helpers::NumberToCurrency
  include Phlex::Rails::Helpers::CollectionCheckBoxes
  include ApplicationHelper

  def initialize(control)
    @control = control
    @reservation ||= Reservation.new
    @pricing = Current.website.pricing
  end

  def view_template
    form_with model: @reservation, id: "form", data: { controller: "reservation" } do |f|
      plain f.hidden_field :form_id, value: @control.form_id
      render_personal_data(f)
      render_pricing(f)
      render_date(f)
      render_submit(f)
    end
  end

  private


  def render_personal_data(f)
    div(id: "reservation_form") do
      div(class: "mb-4 flex space-x-2 items-center bg-primary/10 py-2 px-2") do
        span(class: "w-6 h-6 bg-primary text-white flex items-center font-bold justify-center rounded-full") { "1" }
        p(class: "font-medium text-black") { "Osobní údaje" }
      end
      div(class: "mt-4 flex flex-col space-y-4 w-full") do
        div(class: "flex space-x-5") do
          div(class: "w-full") do
            label(class: "label") { "Jméno" }
            plain f.text_field :name, class: "text-field"
          end

          div(class: "w-full") do
            label(class: "label") { "Příjmení" }
            plain f.text_field :surname, class: "text-field"
          end
        end

        div(class: "flex flex-col space-y-4 w-full") do
          div(class: "flex space-x-5") do
            div(class: "w-full") do
              label(class: "label") { " E-mail " }
              plain f.email_field :email, class: "text-field"
            end

            div(class: "w-full") do
              label(class: "label") { " Telefon " }
              plain f.phone_field :phone, class: "text-field"
            end
          end
        end
      end
    end
  end

  def render_pricing(f)
    div(class: "mt-4") do
      div(class: "mt-4 flex space-x-2 items-center bg-primary/10 py-2 px-2") do
        span(class: "w-6 h-6 bg-primary text-white flex items-center font-bold justify-center rounded-full") { "2" }
        p(class: "font-medium text-black") { "Služby" }
      end
      div(class: "bg-gray-50 py-2 px-3") do
        @pricing.each do |pricing|
          pricing.pricing_sections.each do |pricing_section|
            p(class: "font-medium") { pricing_section.name }
            div(class: "grid grid-cols-1 pt-2 space-y-4") do
              collection_check_boxes(:pricing_section, :pricing_item_ids, pricing_section.pricing_items, :id, :name) do |b|
                label(class: "flex justify-between") do
                  div(class: "flex items-center h-5") do
                    plain b.check_box(
                      class: "w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600",
                      data: { 'reservation-target': "pricingItem", action: "change->reservation#onUpdatePricing", duration: b.object.duration, price: b.object.price }
                    )
                  end

                  div(class: "ms-2 text-sm flex-grow -mt-0.5") do
                    span(
                      for: b.object.name,
                      class: "font-medium text-gray-900 dark:text-gray-300"
                    ) { b.text }

                    p(
                      id: "helper-checkbox-text",
                      class: "text-xs font-normal text-gray-500 dark:text-gray-300"
                    ) { b.object.description }
                  end

                  div(class: "text-right flex items-center space-x-2 whitespace-nowrap") do
                    span(class: "text-xs text-gray-500") { b.object.duration.to_s + " min" }
                    p(class: "font-medium") { price_formatted(b.object) }
                  end
                end
              end
            end
          end
        end
      end
    end
  end

  def render_date(f)
    div(class: "mt-4 mb-4 flex space-x-2 items-center bg-primary/10 py-2 px-2") do
      span(class: "w-6 h-6 bg-primary text-white flex items-center font-bold justify-center rounded-full") { "3" }
      p(class: "font-medium text-black") { "Datum a čas" }
    end
    div(class: "hidden flex space-x-5 mt-4", data: { 'reservation-target': "pricing_views" }) do
      div(class: "w-full sm:w-1/2") do
        div(class: "mb-5") do
          label(for: "date", class: "label") { " Datum " }
          plain f.text_field :date, class: "hidden text-field", data: { 'reservation-target': "date" }
        end
      end
      div(class: "w-full sm:w-1/2") do
        div(class: "mb-5") do
          label(for: "date", class: "label") { " Čas " }
          div(class: "grid grid-cols-3 gap-2", id: "reservation_form_times", data: { 'reservation-target': "times" })
          div(class: "bg-yellow-50 py-2 text-center mt-2", data: { 'reservation-target': "emptyDateAlert" }) { "Vyberte datum." }
        end
      end
    end

    div(class: "bg-yellow-50 mb-4 p-3 text-center", data: { 'reservation-target': "emptyPricingAlert" }) do
      p(class: "text-sm text-yellow-800") { "Vyberte nejdříve služby." }
    end
  end

  def render_submit(f)
    div(class: "flex justify-between") do
      div do
        button(
          type: "submit",
          class:
            "rounded-md bg-primary hover:bg-primary/90 py-3 px-4 text-center text-base font-semibold text-white outline-none"
        ) { " Vytvořit rezervaci " }
      end

      div(id: "reservation_form_errors")
    end
  end
end
