module BlockControls
  class ParagraphControl < Phlex::HTML
    include Phlex::Rails::Helpers::Tag
    include Phlex::Rails::Helpers::SimpleFormat
    include Phlex::Rails::Helpers::Sanitize

    register_element :block_control

    def initialize(paragraph_block_control_object)
      @control = paragraph_block_control_object
      @content = @control.content
      @text = @control.text
      @id = @control.id
    end

    def view_template
      block_control(id: %(control-#{@id}), class: %(control-container)) {  @text.html_safe } if @text.present?
    end

    def render_image(block)
      html_class = []
      html_class.push('w-full') if block[:data][:stretched]

      if block[:data][:withBackground]
        html_class.push('bg-gray-100')
        html_class.push('p-4')
      end

      img(src: block[:data][:file][:url], class: html_class)
    end

    def display_list(items)
      ul do
        items.each do |item|
          li { item[:content].html_safe }
        end
      end
    end
  end
end
