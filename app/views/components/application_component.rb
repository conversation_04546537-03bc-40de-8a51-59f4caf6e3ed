# frozen_string_literal: true

class ApplicationComponent < Phlex::HTML
  include Phlex::Rails::Helpers::Routes
  include Phlex::Rails::Helpers::Request

  register_element :block_container

  if Rails.env.development?
    def before_template
      comment { "Before #{self.class.name}" }
      super
    end
  end

  def theme
    @theme ||= Current.website.theme
  end

  private

  def admin?
    true
  end

  def controller
    # request.controller_class
  end
end
