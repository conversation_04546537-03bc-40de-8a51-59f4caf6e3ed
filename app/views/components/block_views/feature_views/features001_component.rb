module BlockViews
  class FeatureViews::Features001Component < BaseComponent
    def view_template
      container do
        inner_container do
          content_container do
            block_object.controls.each do |control|
              render control.component
            end

            media_container do
              render ::Ui::Features(
                features,
                layout: block_object.media.options[:layout] || :default,
                columns: 3,
                gap: block_object.media.options[:gap] || 3
              )
            end
          end
        end
      end
    end

    private

    def features
      block_object.media.items
    end
  end
end
