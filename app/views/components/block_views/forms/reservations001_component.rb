class BlockViews::Forms::Reservations001Component < ApplicationComponent
  include BlockControls
  def initialize(block)
    @block_object = block
    @form = Current.website.forms.find_by(form_type: :contact)
  end

  def allowed?
    @form.present? && @form.enabled
  end

  def view_template
    return
    section do
      div(class: "max-w-xl mx-auto rounded bg-white p-5") do
        render ContainerControl.new(class: "flex flex-col space-y-3") do
          @block_object.container(:content).controls.each do |control|
            render control.component
          end
        end

        @block_object.container(:form).controls.each do |control|
          render ReservationFormControl.new(control)
        end
      end
    end
  end
end
