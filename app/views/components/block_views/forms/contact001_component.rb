class BlockViews::Forms::Contact001Component < ApplicationComponent
  include BlockControls
  def initialize(block)
    @block_object = block
    @form = Current.website.forms.find_by(form_type: :contact)
  end

  def allowed?
    @form.present? && @form.enabled
  end

  def view_template
    div(class: "relative isolate bg-white px-6 py-12 sm:py-20 lg:px-8") do
      svg(
        class:
          "absolute inset-0 -z-10 h-full w-full stroke-gray-200 [mask-image:radial-gradient(100%_100%_at_top_right,white,transparent)]",
        aria_hidden: "true"
      ) do |s|
        s.defs do
          s.pattern(
            id: "83fd4e5a-9d52-42fc-97b6-718e5d7ee527",
            width: "200",
            height: "200",
            x: "50%",
            y: "-64",
            patternunits: "userSpaceOnUse"
          ) { s.path(d: "M100 200V.5M.5 .5H200", fill: "none") }
        end
        s.svg(x: "50%", y: "-64", class: "overflow-visible fill-gray-50") do
          s.path(
            d:
              "M-100.5 0h201v201h-201Z M699.5 0h201v201h-201Z M499.5 400h201v201h-201Z M299.5 800h201v201h-201Z",
            stroke_width: "0"
          )
        end
        s.rect(
          width: "100%",
          height: "100%",
          stroke_width: "0",
          fill: "url(#83fd4e5a-9d52-42fc-97b6-718e5d7ee527)"
        )
      end
      div(class: "mx-auto max-w-xl lg:max-w-4xl") do
        @block_object.container(:content).controls.each do |control|
          render control.component
        end
        div(class: "mt-8 flex flex-col gap-16 sm:gap-y-20 lg:flex-row") do
          @block_object.container(:form).controls.each do |control|
            render control.component
          end
          div(class: "lg:w-80 lg:flex-none") do
            figure(class: "mt-4") do
              blockquote(class: "font-semibold leading-8") do
                @block_object.container(:contact).controls.each do |control|
                  render control.component
                end
              end
            end
          end
        end
      end
    end
  end
end
