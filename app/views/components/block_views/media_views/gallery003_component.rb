module BlockViews
  class MediaViews::Gallery003Component < ApplicationComponent
    include Phlex::Rails::Helpers::ImageTag
    include Phlex::Rails::Helpers::L

    def initialize(block)
      @block_object = block
      @media = @block_object.media || Media.where(layout: "Gallery").last
    end

    def view_template
      div(class: "#{@block_object.inner_container_class} mx-auto") do
          div(class: "flex flex-col sm:flex-row sm:space-x-28") do
            div(
              data: { controller: "splide" },
              class: "splide",
            ) do
              div(class: "splide__track") do
                ul(class: "splide__list") do
                  @media.images.each do |image|
                    li(class: "splide__slide") do
                      image_tag image.variant(resize_to_fill: [ 2000, 600 ]), class: "w-full h-full object-cover"
                    end
                  end
                end
              end
            end
          end
      end
    end
  end
end
