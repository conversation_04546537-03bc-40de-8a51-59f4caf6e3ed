module BlockViews
  class MediaViews::Instagram001Component < ApplicationComponent
    include Phlex::Rails::Helpers::Image<PERSON>ag


    def initialize(block_object)
      @block_object = block_object
      @instagram_media_object = block_object.media
      @posts = @instagram_media_object.media_items
    end

    def view_template
      render BlockContainer.new(@block_object) do |container|
        div(class: "#{@block_object.inner_container_class} #{@block_object.gap_y_class} flex flex-col") do
          @block_object.controls.each do |control|
            render control.component
          end

          div(class: "grid grid-cols-2 sm:grid-cols-4 gap-1 media_blocks-center") do
            @posts.each do |post|
              div(class: "relative") do
                image_tag post[:data][:image], loading: :lazy
                div(class: "absolute top-0 left-0") do
                  span(
                    class:
                      "flex py-1 px-2 text-black text-sm bg-white/90 font-medium"
                  )
                end
              end
            end
          end
        end
      end
    end
  end
end
