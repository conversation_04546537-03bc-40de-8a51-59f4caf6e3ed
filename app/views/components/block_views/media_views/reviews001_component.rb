module BlockViews
  class MediaViews::Reviews001Component < ApplicationComponent
    include Phlex::Rails::Helpers::ImageTag
    include Phlex::Rails::Helpers::L

    def initialize(block)
      @block_object = block
      @reviews = block.posts
    end

    def view_template
        h2(class: "heading") { "Recenze zákazníků" }
          div(class: "mt-2 flex items-center justify-center gap-x-3 py-8") do
            @reviews.each do |review|
              div(class: "w-1/5 self-stretch rounded-md bg-white px-5 py-6 shadow") do
                if review.image.attached?
                  image_tag review.image, class: "h-11 w-11 mx-auto"
                else
                  span(
                    class:
                      "mx-auto flex h-11 w-11 items-center justify-center rounded-full bg-gray-200 font-medium"
                  ) { plain review.author_initials }
                end
                div(class: "flex flex-col items-center justify-center") do
                  div(class: "my-3 flex justify-center") do
                    (1..(review.rating.to_i)).each do
                      svg(
                        xmlns: "http://www.w3.org/2000/svg",
                        viewbox: "0 0 16 16",
                        fill: "currentColor",
                        class: "text-orange-600 size-3"
                      ) do |s|
                        s.path(
                          fill_rule: "evenodd",
                          d:
                            "M8 1.75a.75.75 0 0 1 .692.462l1.41 3.393 3.664.293a.75.75 0 0 1 .428 1.317l-2.791 2.39.853 3.575a.75.75 0 0 1-1.12.814L7.998 12.08l-3.135 1.915a.75.75 0 0 1-1.12-.814l.852-3.574-2.79-2.39a.75.75 0 0 1 .427-1.318l3.663-.293 1.41-3.393A.75.75 0 0 1 8 1.75Z",
                          clip_rule: "evenodd"
                        )
                      end
                    end
                  end
                  p(class: "text-center font-medium") do
                    plain review.anonymized_author_name
                  end
                  p(class: "py-2 text-center text-sm") do
                    plain review.content_preview
                  end
                end
              end
          end
        end
      end
  end
end
