# frozen_string_literal: true

module BlockViews
  class MediaViews::Features001Component < ApplicationComponent
    def initialize(block)
      @block_object = block

      @block_object.container(:content).heading?
    end

    def view_template
      div(class: "max-w-5xl mx-auto") do
        div(class: "text-center mb-8") do
          @block_object.controls.each do |control|
            render control.component
          end
        end

        div(class: "mx-auto max-w-2xl lg:max-w-none") do
          dl(class: "grid max-w-xl grid-cols-1 gap-y-16 lg:max-w-none lg:grid-cols-3 divide-x divide-gray-100") do
            @block_object.container(:features).controls.each do |feature_control|
              render_feature(feature_control)
            end
          end
        end
      end
    end

    def render_feature(feature_control)
      div(class: "flex flex-col px-10") do
        dt(class: "flex items-center space-x-2 text-base font-semibold leading-7 text-left") do
          div(class: "w-6 h-6 bg-accent rounded-full flex items-center justify-center") do
            svg(
              xmlns: "http://www.w3.org/2000/svg",
              viewbox: "0 0 16 16",
              fill: "currentColor",
              class: "size-4 text-accent-text"
            ) do |s|
              s.path(
                fill_rule: "evenodd",
                d:
                  "M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z",
                clip_rule: "evenodd"
              )
            end
          end

          span { feature_control.title }
        end
        dd(class: "mt-2 flex flex-auto flex-col text-base leading-7 text-gray-600") do
          p(class: "flex-auto text-sm text-left") { feature_control.text }
        end
      end
    end
  end
end
