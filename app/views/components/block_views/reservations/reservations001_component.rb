class BlockViews::Reservations::Reservations001Component < Phlex::HTML
  include Phlex::Rails::Helpers::EmailField
  include Phlex::Rails::Helpers::FormWith
  include Phlex::Rails::Helpers::NumberField
  include Phlex::Rails::Helpers::TelephoneField
  include Phlex::Rails::Helpers::TextArea
  include Phlex::Rails::Helpers::TextField
  include Phlex::Rails::Helpers::TurboFrameTag

  def initialize(block)
    @block_object = block
  end

  def view_template
    section(class: "", style: "background: #{@block_object.theme}") do
      form_with model: Reservation.new, id: "reservation_form" do |f|
        div(class: "flex items-center justify-center p-12") do
          div(class: "mx-auto w-full max-w-[550px] bg-primary/10 p-5") do
            h3(class: "font-medium pb-3 text-xl") { "Online rezervace" }
            div id: "reservation_form_errors", class: "mt-6"
            div(class: "-mx-3 flex flex-wrap") do
              div(class: "w-full px-3") do
                div(class: "mb-5") do
                  label(for: "fName", class: "label") { " Jméno a příjmení " }
                  plain f.text_field :name, class: "text-field"
                end
              end
            end
            div(class: "flex space-x-4 mb-5") do
              div(class: "w-1/2") do
                label(class: "label") { " E-mail " }
                plain f.email_field :email, class: "text-field"
              end
              div(class: "w-1/2") do
                label(class: "label") { " Telefon " }
                plain f.telephone_field :phone, class: "text-field"
              end
            end
            div(class: "flex space-x-5") do
              div(class: "w-full sm:w-1/2") do
                div(class: "mb-5") do
                  label(for: "date", class: "label") { " Datum " }
                  plain f.text_field :date,
                                     class: "text-field",
                                     data: {
                                       controller: "air-datepicker"
                                     }
                end
              end
              div(class: "w-full sm:w-1/2") do
                div(class: "mb-5") do
                  label(class: "label") { " Čas " }
                  plain f.text_field :time,
                                     class: "text-field",
                                     data: {
                                       controller: "air-datepicker",
                                       "air-datepicker-options-value":
                                         '{ "onlyTime": true }'
                                     }
                end
              end
            end
            div(class: "mb-5") do
              label(class: "label") { " Počet hostů " }
              plain f.number_field :guests, class: "text-field"
            end
            div(class: "mb-3") do
              label(class: "label") { "Poznámka k rezervaci" }
              plain f.text_area :note, class: "text-field"
            end
            div do
              button(
                layout: "submit",
                class:
                  "hover:shadow-form rounded-md bg-primary py-3 px-8 text-center text-base font-semibold text-white outline-none"
              ) { " Odeslat rezervaci " }
            end
          end
        end
      end
    end
  end
end
