class BlockViews::Layout::Header001Component < ApplicationComponent
  include Phlex::Rails::Helpers::ImageTag
  include <PERSON>Helper

  def initialize(block)
    @block_object = block
    @page_nodes ||= Page.where(locale: I18n.locale).order(:position).arrange
  end

  def view_template
    div(class: "#{@block_object.inner_container_class} mx-auto flex items-center justify-between p-3") do
      render_logo
      render_navigation
    end
  end

  def render_logo
    div(class: "flex lg:flex-1") do
      a(href: root_path) { display_logo(theme.logo) }
    end
  end

  def render_navigation
    div(class: "hidden lg:flex lg:gap-x-4") do
      @page_nodes.each do |page, children|
        if children.any?
          div(
            class: "relative",
            data_controller: "dropdown",
            data_action:
              " click@ window->dropdown#hide touchstart@window->dropdown#hide keydown.up->dropdown#previousItem keydown.down->dropdown#nextItem keydown.esc->dropdown#hide"
          ) do
            button(
              data_action: "dropdown#toggle:stop",
              layout: "button",
              class: "flex items-center gap-x-1 font-medium text-sm leading-6 p-1.5",
              style: "color:#000",
              aria_expanded: "false"
            ) do
              plain page.title
              svg(
                class: "h-5 w-5 flex-none",
                style: "color:#000",
                viewbox: "0 0 20 20",
                fill: "currentColor",
                aria_hidden: "true"
              ) do |s|
                s.path(
                  fill_rule: "evenodd",
                  d:
                    "M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z",
                  clip_rule: "evenodd"
                )
              end
            end
            div(data_dropdown_target: "menu", class: "hidden relative z-50") do
              div(
                class:
                  "absolute -left-24 top-full z-10 mt-3 w-64 bg-white rounded p-2 shadow-lg ring-1 ring-gray-900/5"
              ) do
                children.each do |child_page, child_pages|
                  div(
                    class: "relative p-3 hover:bg-gray-100",
                    data_dropdown_target: "menuItem"
                  ) do
                    a(
                      href: page_path_resolver(child_page),
                      class: "block text-sm font-medium leading-6 text-black"
                    ) do
                      plain child_page.title
                      span(class: "absolute inset-0")
                    end
                  end
                end
              end
            end
          end
        else
          a(href: page_path_resolver(page), target: (page.is_link? ? :_blank : nil), class: cta_class(page)) { page.title }
        end
      end
    end
  end

  def cta_class(page)
    return "cta inline-flex rounded-md px-3.5 py-2.5 text-sm font-semibold shadow-sm" if page.cta?
    "link text-sm inline-flex rounded-md font-medium py-2.5 px-2 cursor-pointer hover:bg-gray-100"
  end
end
