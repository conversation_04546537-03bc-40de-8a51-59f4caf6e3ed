module BlockViews
  class TeamViews::Team001Component < BaseComponent
    def view_template
      container do
        InnerContainer(block_object) do
          ContentContainer(block_object, css: "mx-auto") do
          div(id: "block-controls-#{@block_object.id}",
              class: "flex flex-col text-center mx-auto #{@block_object.gap_y_class}") do
            @block_object.controls.each do |control|
              render control.component
            end
          end

          ul(role: "list",
             class: "mx-auto grid grid-cols-1 mt-20 gap-x-12 gap-y-24 sm:gap-y-0 sm:grid-cols-2") do
            @block_object.media.items.each do |member|
              render_team_member(member)
            end
          end
        end
        end
      end
    end

    private

    def render_team_member(member)
      li(class: "p-4 bg-base-100 rounded-box", data: { theme: "white" }) do
        div(class: "flex flex-col items-center gap-y-4") do
          div(class:"-mt-20 bg-base-100 p-3 rounded-full") do
          render Ui::Image(member.image, resize_options: { resize_to_fill: [120, 120] }, classes: "aspect-[1/1] rounded-full object-cover")
          end
          # Info
          div(class: "text-center") do
            p(class: "text-base font-semibold leading-7 tracking-tight") { plain member.title }
            p(class: "text-sm font-semibold leading-6") { plain member.data[:job_position] }
            p(class: "text-sm leading-6 mt-2") { plain member.body }
          end
        end
      end
    end
  end
end
