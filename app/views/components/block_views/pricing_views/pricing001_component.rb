module BlockViews
  class PricingViews::Pricing001Component <  BaseComponent
    include BlockControls
    include ApplicationHelper
    include Phlex::Rails::Helpers::NumberToCurrency

    def pricing_items
      if @block_object.pricing_id.nil?
        @pricing = Pricing.active.where(locale: I18n.locale).includes(pricing_sections: :pricing_items)
      else
        @pricing = [Pricing.find(@block_object.pricing_id).includes(pricing_sections: :pricing_items)]
      end
    end

    def component_allowed?
      true
    end

    def view_template
      container do
        inner_container do
          content_container do
          div(id:"block-controls-#{@block_object.id}", class: "mx-auto flex flex-col") do
            @block_object.controls.each do |control|
              render control.component
            end
          end

          pricing_items.each do |pricing|
            div(class: "flex flex-col space-y-2 w-full") do
            pricing.pricing_sections.each do |section|
              section do
                h4 { section.name }
                div(class: "px-3") do
                  ul(role: "list", class: "list mt-2") do
                    section.pricing_items.each do |item|
                      render_item(item)
                    end
                  end
                end
              end
            end
            end
            end
          end
        end
      end
    end

    private

    def border_for_item(item)
      "item after:border-base-content/40" if item.price
    end

    def render_item(item)
      li(
        class:
          "dish flex flex-wrap items-center justify-between px-2 gap-y-4 py-2 sm:flex-nowrap "
      ) do
        div(class: border_for_item(item)) do
          div(class: "flex space-x-2 items-center") do
            span(class: "text-sm font-semibold leading-6 text-gray-900") { item.name }
            dd(class: "flex text-xs text-gray-500 items-center space-x-1") do
              svg(
                xmlns: "http://www.w3.org/2000/svg",
                fill: "none",
                viewbox: "0 0 24 24",
                stroke_width: "1.5",
                stroke: "currentColor",
                class: "size-4"
              ) do |s|
                s.path(
                  stroke_linecap: "round",
                  stroke_linejoin: "round",
                  d: "M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                )
              end
              span { "#{item.duration}min" }
            end if item.duration&.positive?
          end
          div(
            class:
              "mt-0.5 flex items-center gap-x-2 text-sm leading-5 text-gray-600"
          ) do
            span do
              item.description
            end
          end
        end
        dl(
          class:
            "flex w-full flex-none justify-between gap-x-8 sm:w-auto"
        ) do
          div(class: "flex items-center text-sm leading-3 space-x-5") do
            dd(class: "text-sm leading-3 font-medium") do
              price_formatted item
            end
          end
        end
      end
    end
  end
end
