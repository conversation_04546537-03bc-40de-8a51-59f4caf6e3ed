module BlockViews
  class HeroViews::Hero002Component < BaseComponent
    def view_template
      container do
        inner_container do
            div(class: "absolute top-0 right-0 -mt-26 mr-20 hidden lg:block ") do
              div(class: "w-36 h-36 rounded-full bg-accent opacity-40")
            end

            div(class: "absolute bottom-0 left-0 mb-12 -ml-24 hidden lg:block") do
              div(class: "w-32 h-32 rotate-45 bg-accent opacity-30")
            end

            content_container do
              #div(id: "block-controls-#{@block_object.id}", class: "flex flex-col items-center #{@block_object.gap_y_class}") do
                @block_object.controls.each do |control|
                  render control.component
                end
              #end
            end
        end

        overlay
      end
    end
  end
end
