module BlockViews
  class HeroViews::Hero001Component < BaseComponent
    def view_template
      container do
        div(class: "relative isolate overflow-hidden h-screen w-full") do
          overlay

          if @block_object.background_image
            div(class: "absolute inset-0 -z-10 w-full h-full") do
              img(src: url_for(@block_object.background_image), class: "w-full h-full object-cover")
            end
          end


          div(class: "absolute inset-0 flex items-center p-4 sm:p-0 z-30 ") do
            inner_container do
              content_container(css: ["bg-base-100/90"]) do
                @block_object.controls.each do |control|
                  render control.component
                end
              end
            end
          end
        end
      end
    end

    private

    def background_image
      # return url_for(@block_object.background_image) if @block_object.background_image.respond_to?(:attached?) && @block_object.background_image.attached?
      return item_image_url(@block_object.media.items.first) if @block_object.media&.items&.first

      # Default fallback image
      "https://images.unsplash.com/photo-1490645935967-10de6ba17061?q=80&w=3506&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
    end

    def item_image_url(item)
      return url_for(item.image) if item.respond_to?(:image) && item.image.respond_to?(:attached?) && item.image.attached?
      return item.image if item.respond_to?(:image) && item.image.is_a?(String) && item.image.present?
      return item.image_url if item.respond_to?(:image_url) && item.image_url.present?
      return item.data[:image] if item.respond_to?(:data) && item.data.is_a?(Hash) && item.data[:image].present?

      # Default fallback image
      "https://images.unsplash.com/photo-1490645935967-10de6ba17061?q=80&w=3506&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
    end
  end
end
