module BlockViews
  class HeroViews::Hero008Component < BaseComponent
    def view_template
      container do

        inner_container(css: "relative isolate px-6 lg:px-8 z-20") do
          overlay
            content_container(css: "mx-auto text-center") do
              @block_object.controls.each do |control|
                render control.component
              end
            end
          end
      end
    end
  end
end
