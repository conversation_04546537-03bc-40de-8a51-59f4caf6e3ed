module BlockViews
  class Text::Text002Component < ApplicationComponent
    def initialize(block)
      @block_object = block
      @container = @block_object.container(:content)
    end

    def view_template
      div(class: "#{@block_object.inner_container_class} mx-auto") do
        div(class: "flex flex-col #{@block_object.gap_y_class}") do
          @container.controls.each do |control|
            render control.component
          end
        end
      end
    end
  end
end
