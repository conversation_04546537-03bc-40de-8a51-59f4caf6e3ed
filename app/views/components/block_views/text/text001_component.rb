module BlockViews
  class Text::Text001Component < BaseComponent

    def view_template
      container do
        div(id: @block_object.dom_id, class: "#{@block_object.inner_container_class} mx-auto") do
          div(id:"block-controls-#{@block_object.id}", class: "flex flex-col #{@block_object.gap_y_class}") do
            @block_object.controls.each do |control|
              render control.component
            end
          end
        end
      end
    end
  end
end
