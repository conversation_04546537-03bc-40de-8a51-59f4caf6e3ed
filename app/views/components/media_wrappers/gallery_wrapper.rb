class MediaWrappers::GalleryWrapper < Phlex::HTML
  include Phlex::Rails::Helpers::ImageTag

  include BlockControls

  def initialize(media_object)
    @media_object = media_object
    @images = media_object.images
    @layout = media_object.options[:layout] || "grid"
    @classes = "rounded-box grid grid-cols-3 md:grid-cols-#{media_object.options[:inline_items_count] || 3} gap-3"
  end

  def view_template(&)
    div(class: "gallery-wrapper") do
      yield if block_given?
    end
  end

  def image(image, resize_options: {}, **classes)
    if image.is_a?(String)
      img(src: image)
    elsif image.is_a?(ActiveStorage::Attached) || image.is_a?(ActiveStorage::Variant)
      img(class: classes, src: Rails.application.routes.url_helpers.rails_blob_path(image.variant(resize_options || default_resize_options), only_path: true))
    else
      div(class: "w-full h-36 bg-gray-100 border flex rounded items-center justify-center text-gray-600") { "No Image" }
    end
  end

  private

  def default_resize_options
    { resize_to_fill: [300, 300] }
  end
end
