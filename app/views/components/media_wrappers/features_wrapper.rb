class MediaWrappers::FeaturesWrapper < ApplicationComponent
  include Phlex::Rails::Helpers::ImageTag
  include BlockControls
  include IconsHelper

  def initialize(media_object)
    @media_object = media_object
    @features = media_object.items
    @layout = media_object.options[:layout] || "default"
  end

  def view_template
    render_default
  end

  private

  def render_default
    div(class: "features-wrapper grid grid-cols-[repeat(auto-fit,minmax(200px,1fr))] gap-3 w-full") do
      @features.each do |feature|
        div(class: "feature relative p-3 flex flex-col space-y-3") do
          div(class: "flex space-x-3 items-center") do
            span(class: "icon rounded p-2") { Icon.by_name(feature.icon, size: 5) }
            h4 { feature.title }
          end
          p(class: "text-sm") { feature.text }
        end
      end
    end
  end

end
