class Ui::Image < ApplicationComponent
  include Phlex::Rails::Helpers::ImageTag
  include Phlex::Rails::Helpers::AssetPath

  def initialize(media_item_object, resize_options: {}, classes: "", lazy: true, alt: "")
    @media_item_object = media_item_object
    @resize_options = resize_options
    @classes = classes
    @lazy = lazy
    @alt = alt

    if media_item_object.is_a?(String)
     @image = media_item_object
    elsif media_item_object.image_attachment.present?
      @image = media_item_object.image_attachment
    elsif media_item_object.image_url.present?
      @image = media_item_object.image_url
    end
  end

  def view_template
    if @image.nil?
      render_placeholder
    elsif @image.is_a?(String)
      render_url_image
    elsif @image.is_a?(ActiveStorage::Attached::One) && @image.attached?
      render_active_storage_image
    elsif @image.is_a?(ActiveStorage::Attachment)
      render_active_storage_attachment
    elsif @image.respond_to?(:image) && @image.image.attached?
      render_model_with_image
    else
      render_placeholder
    end
  end

  private

  def render_url_image
    img(src: @image, class: @classes, loading: @lazy ? "lazy" : "eager", alt: @alt)
  end

  def render_active_storage_image
    variant = @resize_options.present? ? @image.variant(@resize_options) : @image
    img(src: Rails.application.routes.url_helpers.rails_blob_path(variant, only_path: true), class: @classes, loading: @lazy ? "lazy" : "eager", alt: @alt || @image.filename.to_s)
  end

  def render_active_storage_attachment
    variant = @resize_options.present? ? @image.variant(@resize_options) : @image
    img(src: Rails.application.routes.url_helpers.rails_blob_path(variant, only_path: true), class: @classes, loading: @lazy ? "lazy" : "eager", alt: @alt || @image.filename.to_s)
  end

  def render_model_with_image
    variant = @resize_options.present? ? @image.image.variant(@resize_options) : @image.image
    img(src: url_for(variant), class: @classes, loading: @lazy ? "lazy" : "eager", alt: @alt || @image.image.filename.to_s)
  end

  def render_placeholder
    div(class: "w-full h-full bg-gray-100 flex items-center justify-center #{@classes}") do
      p { "Empty image" }
    end
  end
end
