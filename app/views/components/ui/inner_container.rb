class Ui::InnerContainer < ApplicationComponent
  attr_reader :block_id, :inner_container_layer, :css

  def initialize(block_id, inner_container_layer, css: nil)
    @block_id = block_id
    @inner_container_layer = inner_container_layer

    @css = css
  end

  def view_template
    div(
      data_controller: "block",
      data: theme_data,
      id: dom_id,
      class: "#{rounded_box_class} #{@inner_container_layer.container} #{@inner_container_layer.padding_y} mx-auto overflow-hidden relative" + " #{parse_css}"
    ) do
      div(class: "absolute bottom-0 right-0 text-gray-400 z-50 text-xs my-0.5 mx-2") do
        admin_info
      end if admin?

      yield
    end
  end

  private

  def theme_data
    { theme: @inner_container_layer.theme }
  end

  def admin_info
    "#{@inner_container_layer.theme} #{@block_id} · #{@inner_container_layer.theme}"
  end

  def rounded_box_class
    @inner_container_layer.padding_y == "sm:py-0" ? "rounded-none" : "rounded-box"
  end

  def dom_id
    "block-#{@block_id}-inner-container-layer"
  end
end
