# frozen_string_literal: true

class Ui::MediaContainer < ApplicationComponent
  attr_reader :block_id, :block_object, :css
  def initialize(block_object, css: nil)
    @block_id = block_object.id
    @block_object = block_object
    @css = css
  end

  def view_template
    fragment("fragment_media_wrapper") do
      div(id: dom_id, class: "w-full #{parse_css}") do
        yield
      end
    end
  end

  private

  def dom_id
    "block-#{@block_id}-media-layer"
  end
end
