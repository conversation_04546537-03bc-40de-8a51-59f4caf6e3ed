class Ui::OuterContainer < Phlex::HTML
  attr_reader :block_id, :outer_container_layer, :css, :style

  def initialize(block_id, outer_container_layer, css: nil, style: nil)
    @block_id = block_id
    @outer_container_layer = outer_container_layer
    @css = css
    @style = style
  end

  def view_template
    div(data: { theme: @outer_container_layer.theme }, class: "bg-base-100") do
      div(id: dom_id, data: { theme: @outer_container_layer.theme }, class: "#{@outer_container_layer.container}  mx-auto") do
        div(class: "#{container_classes} #{parse_css}", style: @style) do
          yield
        end
      end
    end
  end

  private

  def container_classes
    "overflow-hidden relative block #{@outer_container_layer.padding_y}"
  end

  def dom_id
    "block-#{@block_id}-outer-container-layer"
  end
end
