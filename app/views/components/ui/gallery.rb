class Ui::Gallery < ApplicationComponent
  def initialize(items, layout: :grid, columns: 3, gap: 3, resize_options: { resize_to_fill: [400, 400] }, item_classes: "")
    @items = items
    @layout = layout
    @columns = columns
    @gap = gap
    @resize_options = resize_options
    @item_classes = item_classes
  end

  def view_template
    case @layout.to_sym
    when :grid
      render_grid
    when :masonry
      render_masonry
    when :slider
      render_slider
    when :strip
      render_strip
    else
      render_grid
    end
  end

  private

  def render_grid
    div(class: "grid grid-cols-2 sm:grid-cols-#{@columns} gap-#{@gap}") do
      @items.each do |item|
        div(class: @item_classes) do
          Ui::Image(item_image(item), resize_options: @resize_options, classes: "w-full h-full object-cover")
        end
      end
    end
  end

  def render_masonry
    div(class: "columns-1 sm:columns-2 md:columns-#{@columns} gap-#{@gap} space-y-#{@gap}") do
      @items.each do |item|
        div(class: "break-inside-avoid #{@item_classes}") do
          Ui::Image(item_image(item), resize_options: @resize_options, classes: "w-full h-auto object-cover")
        end
      end
    end
  end

  def render_slider
    div(data: { controller: "splide" }, class: "relative splide") do
      div(class: "splide__track") do
        ul(class: "splide__list") do
          @items.each do |item|
            li(class: "splide__slide") do
              div(class: @item_classes) do
                Ui::Image(item_image(item), resize_options: @resize_options, classes: "w-full h-full object-cover")
              end
            end
          end
        end
      end
    end
  end

  def render_strip
    div(class: "grid grid-cols-3 md:grid-cols-#{@columns} gap-#{@gap}") do
      @items.each do |item|
        div(class: "even:mt-4 even:lg:mt-10") do
          div(class: "relative rounded-box") { Ui::Image(item.image, resize_options: @resize_options, classes: "w-full rounded-box h-full object-cover") }
        end
      end
    end
  end

  def item_image(item)
    if item.respond_to?(:image)
      item.image
    elsif item.is_a?(Hash) && item[:image].present?
      item[:image]
    elsif item.is_a?(Hash) && item[:image_url].present?
      item[:image_url]
    else
      item
    end
  end
end
