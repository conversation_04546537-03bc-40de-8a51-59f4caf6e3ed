class MediaWrapper < ApplicationComponent
  include MediaWrappers
  #include BlocksHelper

  def initialize(block)
    @block_object = block
  end

  def view_template(&)
    div(id: "media-container-#{@block_object.id}", class: "media-container") do
      if @block_object.media.type == "gallery" && @block_object.media && @block_object.media.items.present?
        render GalleryWrapper.new(@block_object.media, &)
      elsif @block_object.media.type == "features" && @block_object.media && @block_object.media.items.present?
        render FeaturesWrapper.new(@block_object.media, &)
      else
        "noting"
      end
    end
  end
end
