class ReservationsController < ApplicationController
  def index
  end

  def create
    @reservation = Reservation.new(reservation_params)

    if @reservation.save
      # redirect_to root_path, notice: "Reservation created successfully."
    else
      # render 'page_types/show', status: :unprocessable_entity
      # <%= turbo_stream.replace "form", partial: "form" %>
    end
  end

  def dates
    start_date = DateTime.parse(params[:start_date]).beginning_of_day
    end_date = DateTime.parse(params[:end_date]).end_of_day

    render json: Reservation.available_time_slots_by_range(start_date, end_date, params[:duration].to_i)
  end

  def times
    date = Date.parse(params[:date])
    opening_hours = OpeningHour.opening_by_day(date)

    if opening_hours&.opening_hour.nil? || opening_hours&.closing_hour.nil?
      @times = []
      return
    end

    @times = Reservation.available_time_slots(date, opening_hours.opening_hour, opening_hours.closing_hour, params[:duration].to_i).map { |time| time.strftime("%H:%M") }
  end

  private

  def reservation_params
    params.require(:reservation).permit(:name, :email, :phone, :date, :time, :guests, :note)
  end
end
