module SetCurrentRequestDetails
  extend ActiveSupport::Concern

  included do |base|
    if base < ActionController::Base
      set_current_tenant_through_filter
      before_action :set_request_details
    end
  end

  def set_request_details
    Current.request_id = request.uuid
    Current.user_agent = request.user_agent
    Current.ip_address = request.ip

    if params[:website_id]
      Current.website ||= Website.find_by(id: params[:website_id])
    else
      Current.website ||= account_from_domain || account_from_subdomain
    end

    set_current_tenant(Current.website)
  end

  def account_from_domain
    Website.find_by(domain: request.domain)
  end

  def account_from_subdomain
    Website.find_by(domain: request.subdomains.first)
  end
end
