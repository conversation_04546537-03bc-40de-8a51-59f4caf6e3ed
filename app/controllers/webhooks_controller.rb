class WebhooksController < ApplicationController
  skip_before_action :verify_authenticity_token

  def create
    webhook = Webhook.new(webhook_params)

    if webhook.save
      WebhookJob.perform_now(webhook.id)
      render json: { status: "success" }, status: :created
    else
      render json: { errors: webhook.errors.full_messages }, status: :unprocessable_entity
    end
  end

  private

  def webhook_params
    {
      event_type: params[:event],
      payload: JSON.parse(request.raw_post)
    }
  end
end
