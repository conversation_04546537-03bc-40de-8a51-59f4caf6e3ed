class Admin::FormsController < Admin::ApplicationController
  def create_contact_form
    form = Form.new(default_contact_form_params)
    form.save
    redirect_to edit_admin_form_path(form)
  end

  private

  def default_contact_form_params
    {
      name: "Kontaktní formuář",
      form_fields_attributes: [
        { label: "<PERSON><PERSON><PERSON>", field_type: "text", required: true },
        { label: "Email", field_type: "email", required: true },
        { label: "Zpráva", field_type: "textarea", required: true }
      ]
    }
  end
end
