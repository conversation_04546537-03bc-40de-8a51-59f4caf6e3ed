class Admin::Pricing::PricingSectionsController < Admin::ApplicationController
  def update
    @menu = Pricing.find(params[:menu_id])
    @menu_section = @menu.menu_sections.find(params[:id])

    if @menu_section.update(menu_section_params)
      redirect_to edit_admin_menu_path(@menu)
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @menu = Pricing.find(params[:pricing_id])
    @menu_section = @menu.pricing_sections.find(params[:id])
    @menu_section.destroy

    redirect_to edit_admin_pricing_path(@menu_section.pricing)
  end

  def menu_section_params
    params.permit(:position)
  end
end
