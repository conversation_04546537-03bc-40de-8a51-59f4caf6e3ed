class Admin::ApplicationController < ActionController::Base
  layout "admin"

  include Authentication
  include ::SetCurrentRequestDetails

  before_action :set_default_breadcrumb
  before_action :set_theme

  helper_method :current_user

  private

  helper_method :breadcrumbs
  helper_method :available_locales

  before_action :set_website

  def default_url_options(options = {})
    { website_id: @website.try(:id) }.merge(options)
  end

  def set_website
    @website = Website.find(params[:website_id])
  end

  def breadcrumbs
    @breadcrumbs ||= []
  end

  def available_locales
    @available_locales ||= current_tenant.available_locales
  end

  def set_theme
    @theme ||= Rails.application.config.x.themes[:oficina]
    @colors ||= @theme[:colors]
  end

  def add_breadcrumb(name, path = nil)
    breadcrumbs << Breadcrumb.new(name, path)
  end

  def current_user
    Current.user
  end

  def user_signed_in?
    current_user.present?
  end

  def set_default_breadcrumb
    # add_breadcrumb "<PERSON><PERSON>", admin_pages_path(website_id: @website.id)
  end
end
