class Admin::Settings::SocialNetworksController < Admin::ApplicationController
  layout "admin/settings"

  def show
  end

  def update
    if current_tenant.update(social_network_params)
      redirect_to admin_settings_social_networks_path, notice: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ul<PERSON>."
    else
      render :show, status: :unprocessable_entity
    end
  end

  private

  def social_network_params
    params.require(:website).permit(:facebook, :instagram, :x, :youtube)
  end
end
