class Admin::Settings::FormsController < Admin::ApplicationController
  layout "admin/settings"

  def index
    @contact_form = Form.find_by(form_type: :contact) || Form.new(form_type: :contact)
    @reservation_form = Form.find_by(form_type: :reservation) || Form.new(form_type: :reservation)
  end

  def create
    @form = Form.new(form_params)
    if @form.save
      redirect_to admin_settings_forms_path, notice: "Nastavení formulář<PERSON> bylo <PERSON>"
    else
      render :show, status: :unprocessable_entity
    end
  end

  def update
    @form = Form.find(params[:id])
    if @form.update(form_params)
      redirect_to admin_settings_form_path, notice: "Nastavení formulář<PERSON> bylo <PERSON> ul<PERSON>"
    else
      render :show, status: :unprocessable_entity
    end
  end

  private

  def form_params
    params.require(:form).permit(:enabled, :form_type, :minimum_hours_before_reservation, :sms_on_reservation_confirm, :sms_on_reservation_cancel)
  end
end
