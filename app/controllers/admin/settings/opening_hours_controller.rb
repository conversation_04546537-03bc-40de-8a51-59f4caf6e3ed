class Admin::Settings::OpeningHoursController < Admin::ApplicationController
  layout "admin/settings"
  def show
    @website = current_tenant
    @custom_opening_hours = @website.custom_opening_hours

    add_breadcrumb "Nastavení webu", admin_settings_path
    add_breadcrumb "Otevírací doba"
  end

  def update
    @website = current_tenant

    # Ensure that any duplicate custom dates added by the user are automatically filtered out, allowing only unique dates to be saved.
    opening_hours_params = opening_hour_params[:opening_hours_attributes].values
    items_with_null_date = opening_hours_params.select { |attrs| attrs[:date].nil? }
    items_with_non_null_date = opening_hours_params.reject { |attrs| attrs[:date].nil? }

    unique_opening_hours_params = items_with_non_null_date.uniq { |attrs| attrs[:date] }

    @website.assign_attributes(opening_hours_text: opening_hour_params[:opening_hours_text])
    @website.assign_attributes(opening_hours_attributes: items_with_null_date + unique_opening_hours_params)

    if @website.save
      redirect_to admin_settings_opening_hours_path, notice: "Otevírací doba byla <PERSON>"
    else
      @custom_opening_hours = @website.custom_opening_hours

      render :show, status: :unprocessable_entity
    end
  end

  private

  def opening_hour_params
    params.require(:website).permit(:opening_hours_text, opening_hours_attributes: [ :opening_hour, :closing_hour, :id, :day, :date, :_destroy ])
  end
end
