class Admin::ServicesController < Admin::ApplicationController
  before_action :set_breadcrumbs
  before_action :set_service_type, only: %i[edit create update]

  def index
  end

  def edit
    add_breadcrumb @service_type[:name]

    @service = Service.find_by_type(@service_type[:class_name]) || @service_type[:class_name].constantize.new
    @service_name = params[:id].tr("-", "_")
  end

  def create
    @service_class.create(params.permit(*@service_class.permitted_params))

    redirect_to edit_admin_service_path(@service_type[:slug]), notice: "Nastavení bylo ulož<PERSON>"
  end

  def update
    service = Service.find_by_type(@service_type[:class_name])
    service.update(params.permit(*@service_class.permitted_params))

    redirect_to edit_admin_service_path(params[:id]), notice: "Nastavení bylo uloženo"
  end

  private

  def set_service_type
    @service_type = Service.fetch_by_slug(params[:id])
    @service_class = @service_type[:class_name].constantize
  end

  def set_breadcrumbs
    add_breadcrumb "Propojení", (admin_services_path if action_name != "index")
  end
end
