module Admin
  class Content::BlocksController < Content::ContentController
    layout "editor"

    before_action :set_page

    def new
     if params[:add] == "references"
       @current_blocks = Block.visible.all
     else
       @current_blocks = BlockBuilder.blocks_by_type(params[:add].to_sym) if params[:add].present?
     end
    end

    def index
      @blocks = @page.blocks.includes(
        :controls,
      )
    end

    def edit
      @block = Block.find(params[:id])
      @controls = @block.controls_for_locale(@page.locale)
    end

    def create_reference
      @block = Block.find(params[:block_id]).create_reference!(Page.friendly.find(params[:page_id]))
    end

    def create
      block_object = BlockBuilder.build(params[:block_type])
      new_block_instance = Block.initialize_from_block_object(block_object)
      @page.blocks << new_block_instance

      if params[:after].present?
        after_block = Block.find(params[:after])
        new_block_instance.update position: { after: after_block }
      end

      if params[:before].present?
        before_block = Block.find(params[:before])
        new_block_instance.update position: { before: before_block }
      end

      redirect_to admin_page_blocks_path(@page)
    end

    def update
      @block = Block.find(params[:id])

      @block.update(block_params)

      redirect_to admin_page_blocks_path(@page)
    end

    def hide
      @block = Block.find(params[:id])

      @block.update(hidden_at: @block.hidden? ? nil : Time.now)

      redirect_to admin_page_blocks_path(@page)
    end

    def destroy
      @block = Block.find(params[:id])

      @block.destroy

      redirect_to admin_page_blocks_path(@page)
    end

    def block_params
      params.require(:block).permit(
        :background_image, :height,
        :pricing_id,
        options: {},
        pricing_options: {},
        outer_container_layer_attributes: Block::OuterContainerLayer.attribute_names.map(&:to_sym),
        inner_container_layer_attributes: Block::InnerContainerLayer.attribute_names.map(&:to_sym),
        content_layer_attributes: Block::ContentLayer.attribute_names.map(&:to_sym),
      ).tap do |whitelisted|
        whitelisted[:controls_attributes] = block_controls_params
      end
    end

    def sort
      block = @page.blocks.find(params[:id])

      if params[:direction] == "up"
        block.update position: block.position - 1
      elsif params[:direction] == "down"
        block.update position: block.position + 1
      end

      redirect_to admin_page_blocks_path(@page)
    end

    def block_controls_params
      permitted_controls = []

      params[:block][:controls_attributes]&.each do |_, attributes|
        type = attributes[:type]
        klass = type.safe_constantize
        if klass && klass < BlockControl
          # Přidej id a locale do povolených atributů
          permitted_attributes = klass.permitted_attributes + [:id, :locale]
          permitted_controls << attributes.permit(*permitted_attributes)
        else
          permitted_controls << attributes.permit(:id, :locale) # Permit id a locale i pro nerozpoznané typy
        end
      end

      permitted_controls
    end

    def set_page
      @page = Page.friendly.find(params[:page_id])
    end
  end
end
