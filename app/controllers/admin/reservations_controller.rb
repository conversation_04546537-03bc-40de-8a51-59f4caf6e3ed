class Admin::ReservationsController < Admin::ApplicationController
  def index
    @reservations = Reservation.order(created_at: :desc)

    @first_reservation = Reservation.order(:date, :time).take
  end

  def confirm_form
    @reservation = Reservation.find(params[:id])
  end

  def cancel_form
    @reservation = Reservation.find(params[:id])
  end

  def confirm
    @reservation = Reservation.find(params[:id])

    @reservation.confirm!

    redirect_to admin_reservations_path
  end

  def cancel
    @reservation = Reservation.find(params[:id])

    @reservation.cancel!

    redirect_to admin_reservations_path
  end
end
